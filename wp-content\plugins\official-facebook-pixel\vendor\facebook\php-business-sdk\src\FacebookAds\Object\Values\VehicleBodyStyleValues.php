<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static VehicleBodyStyleValues getInstance()
 */
class VehicleBodyStyleValues extends AbstractEnum {

  const CONVERTIBLE = 'CONVERTIBLE';
  const COUPE = 'COUPE';
  const CROSSOVER = 'CROSSOVER';
  const ESTATE = 'ESTATE';
  const GRANDTOURER = 'GRANDTOURER';
  const HATCHBACK = 'HATCHBACK';
  const MINIBUS = 'MINIBUS';
  const MINIVAN = 'MINIVAN';
  const MPV = 'MPV';
  const NONE = 'NONE';
  const OTHER = 'OTHER';
  const PICKUP = 'PICKUP';
  const ROADSTER = 'ROADSTER';
  const SALOON = 'SALOON';
  const SEDAN = 'SEDAN';
  const SMALL_CAR = 'SMALL_CAR';
  const SPORTSCAR = 'SPORTSCAR';
  const SUPERCAR = 'SUPERCAR';
  const SUPERMINI = 'SUPERMINI';
  const SUV = 'SUV';
  const TRUCK = 'TRUCK';
  const VAN = 'VAN';
  const WAGON = 'WAGON';
}
