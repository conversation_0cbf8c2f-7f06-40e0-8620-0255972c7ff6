<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static BusinessAssetSharingAgreementRequestStatusValues getInstance()
 */
class BusinessAssetSharingAgreementRequestStatusValues extends AbstractEnum {

  const APPROVE = 'APPROVE';
  const DECLINE = 'DECLINE';
  const EXPIRED = 'EXPIRED';
  const IN_PROGRESS = 'IN_PROGRESS';
  const PENDING = 'PENDING';
  const PENDING_EMAIL_VERIFICATION = 'PENDING_EMAIL_VERIFICATION';
  const PENDING_INTEGRITY_REVIEW = 'PENDING_INTEGRITY_REVIEW';
}
