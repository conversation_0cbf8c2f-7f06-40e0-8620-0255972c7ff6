<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static VideoCopyrightMatchActionReasonValues getInstance()
 */
class VideoCopyrightMatchActionReasonValues extends AbstractEnum {

  const ARTICLE_17_PREFLAGGING = 'ARTICLE_17_PREFLAGGING';
  const ARTIST_OBJECTION = 'ARTIST_OBJECTION';
  const OBJECTIONABLE_CONTENT = 'OBJECTIONABLE_CONTENT';
  const PREMIUM_MUSIC_VIDEO = 'PREMIUM_MUSIC_VIDEO';
  const PRERELEASE_CONTENT = 'PRERELEASE_CONTENT';
  const PRODUCT_PARAMETERS = 'PRODUCT_PARAMETERS';
  const RESTRICTED_CONTENT = 'RESTRICTED_CONTENT';
  const UNAUTHORIZED_COMMERCIAL_USE = 'UNAUTHORIZED_COMMERCIAL_USE';
}
