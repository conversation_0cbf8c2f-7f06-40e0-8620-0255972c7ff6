<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Fields;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 */

class AdAssetFeedSpecCarouselFields extends AbstractEnum {

  const ADLABELS = 'adlabels';
  const CHILD_ATTACHMENTS = 'child_attachments';
  const MULTI_SHARE_END_CARD = 'multi_share_end_card';
  const MULTI_SHARE_OPTIMIZED = 'multi_share_optimized';

  public function getFieldTypes() {
    return array(
      'adlabels' => 'list<AdAssetFeedSpecAssetLabel>',
      'child_attachments' => 'list<AdAssetFeedSpecCarouselChildAttachment>',
      'multi_share_end_card' => 'bool',
      'multi_share_optimized' => 'bool',
    );
  }
}
