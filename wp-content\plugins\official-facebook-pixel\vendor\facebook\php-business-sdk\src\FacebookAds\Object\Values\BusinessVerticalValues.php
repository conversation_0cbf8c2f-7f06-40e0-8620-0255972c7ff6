<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static BusinessVerticalValues getInstance()
 */
class BusinessVerticalValues extends AbstractEnum {

  const ADVERTISING = 'ADVERTISING';
  const AUTOMOTIVE = 'AUTOMOTIVE';
  const CONSUMER_PACKAGED_GOODS = 'CONSUMER_PACKAGED_GOODS';
  const ECOMMERCE = 'ECOMMERCE';
  const EDUCATION = 'EDUCATION';
  const ENERGY_AND_UTILITIES = 'ENERGY_AND_UTILITIES';
  const ENTERTAINMENT_AND_MEDIA = 'ENTERTAINMENT_AND_MEDIA';
  const FINANCIAL_SERVICES = 'FINANCIAL_SERVICES';
  const GAMING = 'GAMING';
  const GOVERNMENT_AND_POLITICS = 'GOVERNMENT_AND_POLITICS';
  const HEALTH = 'HEALTH';
  const LUXURY = 'LUXURY';
  const MARKETING = 'MARKETING';
  const NON_PROFIT = 'NON_PROFIT';
  const NOT_SET = 'NOT_SET';
  const ORGANIZATIONS_AND_ASSOCIATIONS = 'ORGANIZATIONS_AND_ASSOCIATIONS';
  const OTHER = 'OTHER';
  const PROFESSIONAL_SERVICES = 'PROFESSIONAL_SERVICES';
  const RESTAURANT = 'RESTAURANT';
  const RETAIL = 'RETAIL';
  const TECHNOLOGY = 'TECHNOLOGY';
  const TELECOM = 'TELECOM';
  const TRAVEL = 'TRAVEL';
}
