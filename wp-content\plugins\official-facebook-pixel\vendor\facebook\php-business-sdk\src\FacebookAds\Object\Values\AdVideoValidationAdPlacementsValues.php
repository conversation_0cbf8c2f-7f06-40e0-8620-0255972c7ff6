<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static AdVideoValidationAdPlacementsValues getInstance()
 */
class AdVideoValidationAdPlacementsValues extends AbstractEnum {

  const AUDIENCE_NETWORK_INSTREAM_VIDEO = 'AUDIENCE_NETWORK_INSTREAM_VIDEO';
  const AUDIENCE_NETWORK_INSTREAM_VIDEO_MOBILE = 'AUDIENCE_NETWORK_INSTREAM_VIDEO_MOBILE';
  const AUDIENCE_NETWORK_REWARDED_VIDEO = 'AUDIENCE_NETWORK_REWARDED_VIDEO';
  const DESKTOP_FEED_STANDARD = 'DESKTOP_FEED_STANDARD';
  const FACEBOOK_STORY_MOBILE = 'FACEBOOK_STORY_MOBILE';
  const FACEBOOK_STORY_STICKER_MOBILE = 'FACEBOOK_STORY_STICKER_MOBILE';
  const INSTAGRAM_STANDARD = 'INSTAGRAM_STANDARD';
  const INSTAGRAM_STORY = 'INSTAGRAM_STORY';
  const INSTANT_ARTICLE_STANDARD = 'INSTANT_ARTICLE_STANDARD';
  const INSTREAM_BANNER_DESKTOP = 'INSTREAM_BANNER_DESKTOP';
  const INSTREAM_BANNER_MOBILE = 'INSTREAM_BANNER_MOBILE';
  const INSTREAM_VIDEO_DESKTOP = 'INSTREAM_VIDEO_DESKTOP';
  const INSTREAM_VIDEO_IMAGE = 'INSTREAM_VIDEO_IMAGE';
  const INSTREAM_VIDEO_MOBILE = 'INSTREAM_VIDEO_MOBILE';
  const MESSENGER_MOBILE_INBOX_MEDIA = 'MESSENGER_MOBILE_INBOX_MEDIA';
  const MESSENGER_MOBILE_STORY_MEDIA = 'MESSENGER_MOBILE_STORY_MEDIA';
  const MOBILE_FEED_STANDARD = 'MOBILE_FEED_STANDARD';
  const MOBILE_FULLWIDTH = 'MOBILE_FULLWIDTH';
  const MOBILE_INTERSTITIAL = 'MOBILE_INTERSTITIAL';
  const MOBILE_MEDIUM_RECTANGLE = 'MOBILE_MEDIUM_RECTANGLE';
  const MOBILE_NATIVE = 'MOBILE_NATIVE';
  const RIGHT_COLUMN_STANDARD = 'RIGHT_COLUMN_STANDARD';
  const SUGGESTED_VIDEO_MOBILE = 'SUGGESTED_VIDEO_MOBILE';
}
