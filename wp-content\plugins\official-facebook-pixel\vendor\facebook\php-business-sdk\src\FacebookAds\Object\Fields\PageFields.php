<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Fields;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 */

class PageFields extends AbstractEnum {

  const ABOUT = 'about';
  const ACCESS_TOKEN = 'access_token';
  const AD_CAMPAIGN = 'ad_campaign';
  const AFFILIATION = 'affiliation';
  const APP_ID = 'app_id';
  const ARTISTS_WE_LIKE = 'artists_we_like';
  const ATTIRE = 'attire';
  const AVAILABLE_PROMO_OFFER_IDS = 'available_promo_offer_ids';
  const AWARDS = 'awards';
  const BAND_INTERESTS = 'band_interests';
  const BAND_MEMBERS = 'band_members';
  const BEST_PAGE = 'best_page';
  const BIO = 'bio';
  const BIRTHDAY = 'birthday';
  const BOOKING_AGENT = 'booking_agent';
  const BREAKING_NEWS_USAGE = 'breaking_news_usage';
  const BUILT = 'built';
  const BUSINESS = 'business';
  const CAN_CHECKIN = 'can_checkin';
  const CAN_POST = 'can_post';
  const CATEGORY = 'category';
  const CATEGORY_LIST = 'category_list';
  const CHECKINS = 'checkins';
  const COMPANY_OVERVIEW = 'company_overview';
  const CONNECTED_INSTAGRAM_ACCOUNT = 'connected_instagram_account';
  const CONNECTED_PAGE_BACKED_INSTAGRAM_ACCOUNT = 'connected_page_backed_instagram_account';
  const CONTACT_ADDRESS = 'contact_address';
  const COPYRIGHT_WHITELISTED_IG_PARTNERS = 'copyright_whitelisted_ig_partners';
  const COUNTRY_PAGE_LIKES = 'country_page_likes';
  const COVER = 'cover';
  const CULINARY_TEAM = 'culinary_team';
  const CURRENT_LOCATION = 'current_location';
  const DELIVERY_AND_PICKUP_OPTION_INFO = 'delivery_and_pickup_option_info';
  const DESCRIPTION = 'description';
  const DESCRIPTION_HTML = 'description_html';
  const DIFFERENTLY_OPEN_OFFERINGS = 'differently_open_offerings';
  const DIRECTED_BY = 'directed_by';
  const DISPLAY_SUBTEXT = 'display_subtext';
  const DISPLAYED_MESSAGE_RESPONSE_TIME = 'displayed_message_response_time';
  const DOES_VIEWER_HAVE_PAGE_PERMISSION_LINK_IG = 'does_viewer_have_page_permission_link_ig';
  const EMAILS = 'emails';
  const ENGAGEMENT = 'engagement';
  const FAN_COUNT = 'fan_count';
  const FEATURED_VIDEO = 'featured_video';
  const FEATURES = 'features';
  const FOLLOWERS_COUNT = 'followers_count';
  const FOOD_STYLES = 'food_styles';
  const FOUNDED = 'founded';
  const GENERAL_INFO = 'general_info';
  const GENERAL_MANAGER = 'general_manager';
  const GENRE = 'genre';
  const GLOBAL_BRAND_PAGE_NAME = 'global_brand_page_name';
  const GLOBAL_BRAND_ROOT_ID = 'global_brand_root_id';
  const HAS_ADDED_APP = 'has_added_app';
  const HAS_LEAD_ACCESS = 'has_lead_access';
  const HAS_TRANSITIONED_TO_NEW_PAGE_EXPERIENCE = 'has_transitioned_to_new_page_experience';
  const HAS_WHATSAPP_BUSINESS_NUMBER = 'has_whatsapp_business_number';
  const HAS_WHATSAPP_NUMBER = 'has_whatsapp_number';
  const HOMETOWN = 'hometown';
  const HOURS = 'hours';
  const ID = 'id';
  const IMPRESSUM = 'impressum';
  const INFLUENCES = 'influences';
  const INSTAGRAM_BUSINESS_ACCOUNT = 'instagram_business_account';
  const IS_ALWAYS_OPEN = 'is_always_open';
  const IS_CALLING_ELIGIBLE = 'is_calling_eligible';
  const IS_CHAIN = 'is_chain';
  const IS_COMMUNITY_PAGE = 'is_community_page';
  const IS_ELIGIBLE_FOR_BRANDED_CONTENT = 'is_eligible_for_branded_content';
  const IS_ELIGIBLE_FOR_DISABLE_CONNECT_IG_BTN_FOR_NON_PAGE_ADMIN_AM_WEB = 'is_eligible_for_disable_connect_ig_btn_for_non_page_admin_am_web';
  const IS_MESSENGER_BOT_GET_STARTED_ENABLED = 'is_messenger_bot_get_started_enabled';
  const IS_MESSENGER_PLATFORM_BOT = 'is_messenger_platform_bot';
  const IS_OWNED = 'is_owned';
  const IS_PERMANENTLY_CLOSED = 'is_permanently_closed';
  const IS_PUBLISHED = 'is_published';
  const IS_UNCLAIMED = 'is_unclaimed';
  const IS_VERIFIED = 'is_verified';
  const IS_WEBHOOKS_SUBSCRIBED = 'is_webhooks_subscribed';
  const KEYWORDS = 'keywords';
  const LEADGEN_TOS_ACCEPTANCE_TIME = 'leadgen_tos_acceptance_time';
  const LEADGEN_TOS_ACCEPTED = 'leadgen_tos_accepted';
  const LEADGEN_TOS_ACCEPTING_USER = 'leadgen_tos_accepting_user';
  const LINK = 'link';
  const LOCATION = 'location';
  const MEMBERS = 'members';
  const MERCHANT_ID = 'merchant_id';
  const MERCHANT_REVIEW_STATUS = 'merchant_review_status';
  const MESSAGING_FEATURE_STATUS = 'messaging_feature_status';
  const MESSENGER_ADS_DEFAULT_ICEBREAKERS = 'messenger_ads_default_icebreakers';
  const MESSENGER_ADS_DEFAULT_QUICK_REPLIES = 'messenger_ads_default_quick_replies';
  const MESSENGER_ADS_QUICK_REPLIES_TYPE = 'messenger_ads_quick_replies_type';
  const MINI_SHOP_STOREFRONT = 'mini_shop_storefront';
  const MISSION = 'mission';
  const MPG = 'mpg';
  const NAME = 'name';
  const NAME_WITH_LOCATION_DESCRIPTOR = 'name_with_location_descriptor';
  const NETWORK = 'network';
  const NEW_LIKE_COUNT = 'new_like_count';
  const OFFER_ELIGIBLE = 'offer_eligible';
  const OVERALL_STAR_RATING = 'overall_star_rating';
  const OWNER_BUSINESS = 'owner_business';
  const PAGE_TOKEN = 'page_token';
  const PARENT_PAGE = 'parent_page';
  const PARKING = 'parking';
  const PAYMENT_OPTIONS = 'payment_options';
  const PERSONAL_INFO = 'personal_info';
  const PERSONAL_INTERESTS = 'personal_interests';
  const PHARMA_SAFETY_INFO = 'pharma_safety_info';
  const PHONE = 'phone';
  const PICKUP_OPTIONS = 'pickup_options';
  const PLACE_TYPE = 'place_type';
  const PLOT_OUTLINE = 'plot_outline';
  const PREFERRED_AUDIENCE = 'preferred_audience';
  const PRESS_CONTACT = 'press_contact';
  const PRICE_RANGE = 'price_range';
  const PRIVACY_INFO_URL = 'privacy_info_url';
  const PRODUCED_BY = 'produced_by';
  const PRODUCTS = 'products';
  const PROMOTION_ELIGIBLE = 'promotion_eligible';
  const PROMOTION_INELIGIBLE_REASON = 'promotion_ineligible_reason';
  const PUBLIC_TRANSIT = 'public_transit';
  const RATING_COUNT = 'rating_count';
  const RECIPIENT = 'recipient';
  const RECORD_LABEL = 'record_label';
  const RELEASE_DATE = 'release_date';
  const RESTAURANT_SERVICES = 'restaurant_services';
  const RESTAURANT_SPECIALTIES = 'restaurant_specialties';
  const SCHEDULE = 'schedule';
  const SCREENPLAY_BY = 'screenplay_by';
  const SEASON = 'season';
  const SINGLE_LINE_ADDRESS = 'single_line_address';
  const STARRING = 'starring';
  const START_INFO = 'start_info';
  const STORE_CODE = 'store_code';
  const STORE_LOCATION_DESCRIPTOR = 'store_location_descriptor';
  const STORE_NUMBER = 'store_number';
  const STUDIO = 'studio';
  const SUPPORTS_DONATE_BUTTON_IN_LIVE_VIDEO = 'supports_donate_button_in_live_video';
  const TALKING_ABOUT_COUNT = 'talking_about_count';
  const TEMPORARY_STATUS = 'temporary_status';
  const UNREAD_MESSAGE_COUNT = 'unread_message_count';
  const UNREAD_NOTIF_COUNT = 'unread_notif_count';
  const UNSEEN_MESSAGE_COUNT = 'unseen_message_count';
  const USER_ACCESS_EXPIRE_TIME = 'user_access_expire_time';
  const USERNAME = 'username';
  const VERIFICATION_STATUS = 'verification_status';
  const VOIP_INFO = 'voip_info';
  const WEBSITE = 'website';
  const WERE_HERE_COUNT = 'were_here_count';
  const WHATSAPP_NUMBER = 'whatsapp_number';
  const WRITTEN_BY = 'written_by';

  public function getFieldTypes() {
    return array(
      'about' => 'string',
      'access_token' => 'string',
      'ad_campaign' => 'AdSet',
      'affiliation' => 'string',
      'app_id' => 'string',
      'artists_we_like' => 'string',
      'attire' => 'string',
      'available_promo_offer_ids' => 'list<map<string, list<map<string, string>>>>',
      'awards' => 'string',
      'band_interests' => 'string',
      'band_members' => 'string',
      'best_page' => 'Page',
      'bio' => 'string',
      'birthday' => 'string',
      'booking_agent' => 'string',
      'breaking_news_usage' => 'Object',
      'built' => 'string',
      'business' => 'Object',
      'can_checkin' => 'bool',
      'can_post' => 'bool',
      'category' => 'string',
      'category_list' => 'list<PageCategory>',
      'checkins' => 'unsigned int',
      'company_overview' => 'string',
      'connected_instagram_account' => 'IGUser',
      'connected_page_backed_instagram_account' => 'IGUser',
      'contact_address' => 'MailingAddress',
      'copyright_whitelisted_ig_partners' => 'list<string>',
      'country_page_likes' => 'unsigned int',
      'cover' => 'CoverPhoto',
      'culinary_team' => 'string',
      'current_location' => 'string',
      'delivery_and_pickup_option_info' => 'list<string>',
      'description' => 'string',
      'description_html' => 'string',
      'differently_open_offerings' => 'list<map<string, bool>>',
      'directed_by' => 'string',
      'display_subtext' => 'string',
      'displayed_message_response_time' => 'string',
      'does_viewer_have_page_permission_link_ig' => 'bool',
      'emails' => 'list<string>',
      'engagement' => 'Engagement',
      'fan_count' => 'unsigned int',
      'featured_video' => 'AdVideo',
      'features' => 'string',
      'followers_count' => 'unsigned int',
      'food_styles' => 'list<string>',
      'founded' => 'string',
      'general_info' => 'string',
      'general_manager' => 'string',
      'genre' => 'string',
      'global_brand_page_name' => 'string',
      'global_brand_root_id' => 'string',
      'has_added_app' => 'bool',
      'has_lead_access' => 'HasLeadAccess',
      'has_transitioned_to_new_page_experience' => 'bool',
      'has_whatsapp_business_number' => 'bool',
      'has_whatsapp_number' => 'bool',
      'hometown' => 'string',
      'hours' => 'map<string, string>',
      'id' => 'string',
      'impressum' => 'string',
      'influences' => 'string',
      'instagram_business_account' => 'IGUser',
      'is_always_open' => 'bool',
      'is_calling_eligible' => 'bool',
      'is_chain' => 'bool',
      'is_community_page' => 'bool',
      'is_eligible_for_branded_content' => 'bool',
      'is_eligible_for_disable_connect_ig_btn_for_non_page_admin_am_web' => 'bool',
      'is_messenger_bot_get_started_enabled' => 'bool',
      'is_messenger_platform_bot' => 'bool',
      'is_owned' => 'bool',
      'is_permanently_closed' => 'bool',
      'is_published' => 'bool',
      'is_unclaimed' => 'bool',
      'is_verified' => 'bool',
      'is_webhooks_subscribed' => 'bool',
      'keywords' => 'Object',
      'leadgen_tos_acceptance_time' => 'datetime',
      'leadgen_tos_accepted' => 'bool',
      'leadgen_tos_accepting_user' => 'User',
      'link' => 'string',
      'location' => 'Location',
      'members' => 'string',
      'merchant_id' => 'string',
      'merchant_review_status' => 'string',
      'messaging_feature_status' => 'MessagingFeatureStatus',
      'messenger_ads_default_icebreakers' => 'list<string>',
      'messenger_ads_default_quick_replies' => 'list<string>',
      'messenger_ads_quick_replies_type' => 'string',
      'mini_shop_storefront' => 'Shop',
      'mission' => 'string',
      'mpg' => 'string',
      'name' => 'string',
      'name_with_location_descriptor' => 'string',
      'network' => 'string',
      'new_like_count' => 'unsigned int',
      'offer_eligible' => 'bool',
      'overall_star_rating' => 'float',
      'owner_business' => 'Business',
      'page_token' => 'string',
      'parent_page' => 'Page',
      'parking' => 'PageParking',
      'payment_options' => 'PagePaymentOptions',
      'personal_info' => 'string',
      'personal_interests' => 'string',
      'pharma_safety_info' => 'string',
      'phone' => 'string',
      'pickup_options' => 'list<string>',
      'place_type' => 'string',
      'plot_outline' => 'string',
      'preferred_audience' => 'Targeting',
      'press_contact' => 'string',
      'price_range' => 'string',
      'privacy_info_url' => 'string',
      'produced_by' => 'string',
      'products' => 'string',
      'promotion_eligible' => 'bool',
      'promotion_ineligible_reason' => 'string',
      'public_transit' => 'string',
      'rating_count' => 'unsigned int',
      'recipient' => 'string',
      'record_label' => 'string',
      'release_date' => 'string',
      'restaurant_services' => 'PageRestaurantServices',
      'restaurant_specialties' => 'PageRestaurantSpecialties',
      'schedule' => 'string',
      'screenplay_by' => 'string',
      'season' => 'string',
      'single_line_address' => 'string',
      'starring' => 'string',
      'start_info' => 'PageStartInfo',
      'store_code' => 'string',
      'store_location_descriptor' => 'string',
      'store_number' => 'unsigned int',
      'studio' => 'string',
      'supports_donate_button_in_live_video' => 'bool',
      'talking_about_count' => 'unsigned int',
      'temporary_status' => 'string',
      'unread_message_count' => 'unsigned int',
      'unread_notif_count' => 'unsigned int',
      'unseen_message_count' => 'unsigned int',
      'user_access_expire_time' => 'datetime',
      'username' => 'string',
      'verification_status' => 'string',
      'voip_info' => 'VoipInfo',
      'website' => 'string',
      'were_here_count' => 'unsigned int',
      'whatsapp_number' => 'string',
      'written_by' => 'string',
    );
  }
}
