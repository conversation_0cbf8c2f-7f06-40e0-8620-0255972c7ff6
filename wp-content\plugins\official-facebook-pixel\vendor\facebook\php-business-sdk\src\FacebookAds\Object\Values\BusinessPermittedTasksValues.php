<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static BusinessPermittedTasksValues getInstance()
 */
class BusinessPermittedTasksValues extends AbstractEnum {

  const ADVERTISE = 'ADVERTISE';
  const ANALYZE = 'ANALYZE';
  const CASHIER_ROLE = 'CASHIER_ROLE';
  const CREATE_CONTENT = 'CREATE_CONTENT';
  const MANAGE = 'MANAGE';
  const MANAGE_JOBS = 'MANAGE_JOBS';
  const MANAGE_LEADS = 'MANAGE_LEADS';
  const MESSAGING = 'MESSAGING';
  const MODERATE = 'MODERATE';
  const MODERATE_COMMUNITY = 'MODERATE_COMMUNITY';
  const PAGES_MESSAGING = 'PAGES_MESSAGING';
  const PAGES_MESSAGING_SUBSCRIPTIONS = 'PAGES_MESSAGING_SUBSCRIPTIONS';
  const PROFILE_PLUS_ADVERTISE = 'PROFILE_PLUS_ADVERTISE';
  const PROFILE_PLUS_ANALYZE = 'PROFILE_PLUS_ANALYZE';
  const PROFILE_PLUS_CREATE_CONTENT = 'PROFILE_PLUS_CREATE_CONTENT';
  const PROFILE_PLUS_FACEBOOK_ACCESS = 'PROFILE_PLUS_FACEBOOK_ACCESS';
  const PROFILE_PLUS_FULL_CONTROL = 'PROFILE_PLUS_FULL_CONTROL';
  const PROFILE_PLUS_MANAGE = 'PROFILE_PLUS_MANAGE';
  const PROFILE_PLUS_MANAGE_LEADS = 'PROFILE_PLUS_MANAGE_LEADS';
  const PROFILE_PLUS_MESSAGING = 'PROFILE_PLUS_MESSAGING';
  const PROFILE_PLUS_MODERATE = 'PROFILE_PLUS_MODERATE';
  const PROFILE_PLUS_MODERATE_DELEGATE_COMMUNITY = 'PROFILE_PLUS_MODERATE_DELEGATE_COMMUNITY';
  const PROFILE_PLUS_REVENUE = 'PROFILE_PLUS_REVENUE';
  const READ_PAGE_MAILBOXES = 'READ_PAGE_MAILBOXES';
  const VIEW_MONETIZATION_INSIGHTS = 'VIEW_MONETIZATION_INSIGHTS';
}
