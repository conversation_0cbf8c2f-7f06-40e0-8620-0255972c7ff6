<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static GroupPurposeValues getInstance()
 */
class GroupPurposeValues extends AbstractEnum {

  const CASUAL = 'CASUAL';
  const COWORKERS = 'COWORKERS';
  const CUSTOM = 'CUSTOM';
  const FOR_SALE = 'FOR_SALE';
  const FOR_WORK = 'FOR_WORK';
  const GAME = 'GAME';
  const HEALTH_SUPPORT = 'HEALTH_SUPPORT';
  const JOBS = 'JOBS';
  const LEARNING = 'LEARNING';
  const NONE = 'NONE';
  const PARENTING = 'PARENTING';
  const STREAMER = 'STREAMER';
  const WORK_ANNOUNCEMENT = 'WORK_ANNOUNCEMENT';
  const WORK_DEMO_GROUP = 'WORK_DEMO_GROUP';
  const WORK_DISCUSSION = 'WORK_DISCUSSION';
  const WORK_EPHEMERAL = 'WORK_EPHEMERAL';
  const WORK_FEEDBACK = 'WORK_FEEDBACK';
  const WORK_FOR_SALE = 'WORK_FOR_SALE';
  const WORK_GARDEN = 'WORK_GARDEN';
  const WORK_INTEGRITY = 'WORK_INTEGRITY';
  const WORK_LEARNING = 'WORK_LEARNING';
  const WORK_MENTORSHIP = 'WORK_MENTORSHIP';
  const WORK_MULTI_COMPANY = 'WORK_MULTI_COMPANY';
  const WORK_RECRUITING = 'WORK_RECRUITING';
  const WORK_SOCIAL = 'WORK_SOCIAL';
  const WORK_STAGES = 'WORK_STAGES';
  const WORK_TEAM = 'WORK_TEAM';
  const WORK_TEAMWORK = 'WORK_TEAMWORK';
}
