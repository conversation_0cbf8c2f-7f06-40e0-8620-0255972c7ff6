<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CustomAudienceClaimObjectiveValues getInstance()
 */
class CustomAudienceClaimObjectiveValues extends AbstractEnum {

  const AUTOMOTIVE_MODEL = 'AUTOMOTIVE_MODEL';
  const COLLABORATIVE_ADS = 'COLLABORATIVE_ADS';
  const HOME_LISTING = 'HOME_LISTING';
  const MEDIA_TITLE = 'MEDIA_TITLE';
  const PRODUCT = 'PRODUCT';
  const TRAVEL = 'TRAVEL';
  const VEHICLE = 'VEHICLE';
  const VEHICLE_OFFER = 'VEHICLE_OFFER';
}
