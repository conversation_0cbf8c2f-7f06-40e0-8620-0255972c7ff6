<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static JobOpeningReviewRejectionReasonsValues getInstance()
 */
class JobOpeningReviewRejectionReasonsValues extends AbstractEnum {

  const ADULT_CONTENT = 'ADULT_CONTENT';
  const DISCRIMINATION = 'DISCRIMINATION';
  const DRUGS = 'DRUGS';
  const GENERIC_DEFAULT = 'GENERIC_DEFAULT';
  const ILLEGAL = 'ILLEGAL';
  const IMPERSONATION = 'IMPERSONATION';
  const MISLEADING = 'MISLEADING';
  const MULTILEVEL_MARKETING = 'MULTILEVEL_MARKETING';
  const PERSONAL_INFO = 'PERSONAL_INFO';
  const SEXUAL = 'SEXUAL';
}
