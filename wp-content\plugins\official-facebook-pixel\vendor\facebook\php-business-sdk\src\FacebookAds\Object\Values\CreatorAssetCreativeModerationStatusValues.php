<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CreatorAssetCreativeModerationStatusValues getInstance()
 */
class CreatorAssetCreativeModerationStatusValues extends AbstractEnum {

  const ARCHIVED = 'ARCHIVED';
  const ELIGIBLE = 'ELIGIBLE';
  const EXPIRED = 'EXPIRED';
  const INELIGIBLE = 'INELIGIBLE';
  const IN_REVIEW = 'IN_REVIEW';
  const PAUSED = 'PAUSED';
  const UNKNOWN = 'UNKNOWN';
}
