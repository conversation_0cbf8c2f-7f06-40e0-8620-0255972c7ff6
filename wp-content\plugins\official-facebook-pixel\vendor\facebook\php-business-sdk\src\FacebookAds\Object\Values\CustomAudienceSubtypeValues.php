<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CustomAudienceSubtypeValues getInstance()
 */
class CustomAudienceSubtypeValues extends AbstractEnum {

  const APP = 'APP';
  const BAG_OF_ACCOUNTS = 'BAG_OF_ACCOUNTS';
  const BIDDING = 'BIDDING';
  const CLAIM = 'CLAIM';
  const CUSTOM = 'CUSTOM';
  const ENGAGEMENT = 'ENGAGEMENT';
  const EXCLUSION = 'EXCLUSION';
  const FOX = 'FOX';
  const LOOKALIKE = 'LOOKALIKE';
  const MANAGED = 'MANAGED';
  const MEASUREMENT = 'MEASUREMENT';
  const MESSENGER_SUBSCRIBER_LIST = 'MESSENGER_SUBSCRIBER_LIST';
  const OFFLINE_CONVERSION = 'OFFLINE_CONVERSION';
  const PARTNER = 'PARTNER';
  const PRIMARY = 'PRIMARY';
  const REGULATED_CATEGORIES_AUDIENCE = 'REGULATED_CATEGORIES_AUDIENCE';
  const STUDY_RULE_AUDIENCE = 'STUDY_RULE_AUDIENCE';
  const VIDEO = 'VIDEO';
  const WEBSITE = 'WEBSITE';
}
