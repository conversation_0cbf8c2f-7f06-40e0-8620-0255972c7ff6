{"packages": [{"name": "facebook/php-business-sdk", "version": "22.0.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/facebook/facebook-php-business-sdk.git", "reference": "c83b450089c557a1c64960103f3c658a429bb525"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/facebook/facebook-php-business-sdk/zipball/c83b450089c557a1c64960103f3c658a429bb525", "reference": "c83b450089c557a1c64960103f3c658a429bb525", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.5 || ^7.0", "php": ">=8.0"}, "require-dev": {"mockery/mockery": "1.3.3", "phpunit/phpunit": "~9", "symfony/finder": "~2.6"}, "time": "2025-02-13T21:08:40+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"FacebookAds\\": "src/FacebookAds/"}}, "notification-url": "https://packagist.org/downloads/", "description": "PHP SDK for Facebook Business", "homepage": "https://developers.facebook.com/", "keywords": ["ads", "business", "facebook", "instagram", "page", "sdk"], "support": {"issues": "https://github.com/facebook/facebook-php-business-sdk/issues", "source": "https://github.com/facebook/facebook-php-business-sdk/tree/22.0.0"}, "install-path": "../facebook/php-business-sdk"}, {"name": "techcrunch/wp-async-task", "version": "dev-master", "version_normalized": "dev-master", "source": {"type": "git", "url": "https://github.com/techcrunch/wp-async-task.git", "reference": "9bdbbf9df4ff5179711bb58b9a2451296f6753dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/techcrunch/wp-async-task/zipball/9bdbbf9df4ff5179711bb58b9a2451296f6753dc", "reference": "9bdbbf9df4ff5179711bb58b9a2451296f6753dc", "shasum": ""}, "require-dev": {"10up/wp_mock": "dev-master", "phpunit/phpunit": "*@stable"}, "time": "2016-03-10T17:37:13+00:00", "default-branch": true, "type": "wordpress-plugin", "installation-source": "dist", "autoload": {"classmap": ["wp-async-task.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "role": "developer"}, {"name": "<PERSON>", "role": "developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "developer"}], "description": "Run asynchronous tasks for long-running operations in WordPress", "support": {"issues": "https://github.com/techcrunch/wp-async-task/issues", "source": "https://github.com/techcrunch/wp-async-task/tree/master"}, "install-path": "../techcrunch/wp-async-task"}], "dev": false, "dev-package-names": []}