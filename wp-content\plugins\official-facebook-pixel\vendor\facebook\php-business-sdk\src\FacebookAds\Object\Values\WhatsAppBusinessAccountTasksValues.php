<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static WhatsAppBusinessAccountTasksValues getInstance()
 */
class WhatsAppBusinessAccountTasksValues extends AbstractEnum {

  const DEVELOP = 'DEVELOP';
  const MANAGE = 'MANAGE';
  const MANAGE_EXTENSIONS = 'MANAGE_EXTENSIONS';
  const MANAGE_PHONE = 'MANAGE_PHONE';
  const MANAGE_PHONE_ASSETS = 'MANAGE_PHONE_ASSETS';
  const MANAGE_TEMPLATES = 'MANAGE_TEMPLATES';
  const MESSAGING = 'MESSAGING';
  const VIEW_COST = 'VIEW_COST';
  const VIEW_PHONE_ASSETS = 'VIEW_PHONE_ASSETS';
  const VIEW_TEMPLATES = 'VIEW_TEMPLATES';
}
