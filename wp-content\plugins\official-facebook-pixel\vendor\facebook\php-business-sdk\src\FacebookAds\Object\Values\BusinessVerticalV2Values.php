<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static BusinessVerticalV2Values getInstance()
 */
class BusinessVerticalV2Values extends AbstractEnum {

  const ADVERTISING_AND_MARKETING = 'ADVERTISING_AND_MARKETING';
  const AGRICULTURE = 'AGRICULTURE';
  const AUTOMOTIVE = 'AUTOMOTIVE';
  const BANKING_AND_CREDIT_CARDS = 'BANKING_AND_CREDIT_CARDS';
  const BUSINESS_TO_BUSINESS = 'BUSINESS_TO_BUSINESS';
  const CONSUMER_PACKAGED_GOODS = 'CONSUMER_PACKAGED_GOODS';
  const ECOMMERCE = 'ECOMMERCE';
  const EDUCATION = 'EDUCATION';
  const ENERGY_AND_NATURAL_RESOURCES_AND_UTILITIES = 'ENERGY_AND_NATURAL_RESOURCES_AND_UTILITIES';
  const ENTERTAINMENT_AND_MEDIA = 'ENTERTAINMENT_AND_MEDIA';
  const GAMING = 'GAMING';
  const GOVERNMENT = 'GOVERNMENT';
  const HEALTHCARE_AND_PHARMACEUTICALS_AND_BIOTECH = 'HEALTHCARE_AND_PHARMACEUTICALS_AND_BIOTECH';
  const INSURANCE = 'INSURANCE';
  const NON_PROFIT = 'NON_PROFIT';
  const ORGANIZATIONS_AND_ASSOCIATIONS = 'ORGANIZATIONS_AND_ASSOCIATIONS';
  const POLITICS = 'POLITICS';
  const PROFESSIONAL_SERVICES = 'PROFESSIONAL_SERVICES';
  const PUBLISHING = 'PUBLISHING';
  const RESTAURANTS = 'RESTAURANTS';
  const RETAIL = 'RETAIL';
  const TECHNOLOGY = 'TECHNOLOGY';
  const TELECOM = 'TELECOM';
  const TRAVEL = 'TRAVEL';
}
