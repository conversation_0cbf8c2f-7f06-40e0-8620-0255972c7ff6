<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CustomConversionStatsResultAggregationValues getInstance()
 */
class CustomConversionStatsResultAggregationValues extends AbstractEnum {

  const COUNT = 'count';
  const DEVICE_TYPE = 'device_type';
  const HOST = 'host';
  const PIXEL_FIRE = 'pixel_fire';
  const UNMATCHED_COUNT = 'unmatched_count';
  const UNMATCHED_USD_AMOUNT = 'unmatched_usd_amount';
  const URL = 'url';
  const USD_AMOUNT = 'usd_amount';
}
