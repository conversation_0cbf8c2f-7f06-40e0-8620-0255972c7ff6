<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static SystemUserRoleValues getInstance()
 */
class SystemUserRoleValues extends AbstractEnum {

  const ADMIN = 'ADMIN';
  const ADS_RIGHTS_REVIEWER = 'ADS_RIGHTS_REVIEWER';
  const VALUE_DEFAULT = 'DEFAULT';
  const DEVELOPER = 'DEVELOPER';
  const EMPLOYEE = 'EMPLOYEE';
  const FINANCE_ANALYST = 'FINANCE_ANALYST';
  const FINANCE_EDIT = 'FINANCE_EDIT';
  const FINANCE_EDITOR = 'FINANCE_EDITOR';
  const FINANCE_VIEW = 'FINANCE_VIEW';
  const MANAGE = 'MANAGE';
  const PARTNER_CENTER_ADMIN = 'PARTNER_CENTER_ADMIN';
  const PARTNER_CENTER_ANALYST = 'PARTNER_CENTER_ANALYST';
  const PARTNER_CENTER_EDUCATION = 'PARTNER_CENTER_EDUCATION';
  const PARTNER_CENTER_MARKETING = 'PARTNER_CENTER_MARKETING';
  const PARTNER_CENTER_OPERATIONS = 'PARTNER_CENTER_OPERATIONS';
}
