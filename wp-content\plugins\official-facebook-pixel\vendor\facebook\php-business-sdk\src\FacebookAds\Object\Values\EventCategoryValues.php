<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static EventCategoryValues getInstance()
 */
class EventCategoryValues extends AbstractEnum {

  const CLASSIC_LITERATURE = 'CLASSIC_LITERATURE';
  const COMEDY = 'COMEDY';
  const CRAFTS = 'CRAFTS';
  const DANCE = 'DANCE';
  const DRINKS = 'DRINKS';
  const FITNESS_AND_WORKOUTS = 'FITNESS_AND_WORKOUTS';
  const FOODS = 'FOODS';
  const GAMES = 'GAMES';
  const GARDENING = 'GARDENING';
  const HEALTHY_LIVING_AND_SELF_CARE = 'HEALTHY_LIVING_AND_SELF_CARE';
  const HEALTH_AND_MEDICAL = 'HEALTH_AND_MEDICAL';
  const HOME_AND_GARDEN = 'HOME_AND_GARDEN';
  const MUSIC_AND_AUDIO = 'MUSIC_AND_AUDIO';
  const PARTIES = 'PARTIES';
  const PROFESSIONAL_NETWORKING = 'PROFESSIONAL_NETWORKING';
  const RELIGIONS = 'RELIGIONS';
  const SHOPPING_EVENT = 'SHOPPING_EVENT';
  const SOCIAL_ISSUES = 'SOCIAL_ISSUES';
  const SPORTS = 'SPORTS';
  const THEATER = 'THEATER';
  const TV_AND_MOVIES = 'TV_AND_MOVIES';
  const VISUAL_ARTS = 'VISUAL_ARTS';
}
