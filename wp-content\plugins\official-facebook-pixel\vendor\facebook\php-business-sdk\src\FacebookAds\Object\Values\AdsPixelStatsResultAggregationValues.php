<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static AdsPixelStatsResultAggregationValues getInstance()
 */
class AdsPixelStatsResultAggregationValues extends AbstractEnum {

  const BROWSER_TYPE = 'browser_type';
  const CUSTOM_DATA_FIELD = 'custom_data_field';
  const DEVICE_OS = 'device_os';
  const DEVICE_TYPE = 'device_type';
  const EVENT = 'event';
  const EVENT_DETECTION_METHOD = 'event_detection_method';
  const EVENT_PROCESSING_RESULTS = 'event_processing_results';
  const EVENT_SOURCE = 'event_source';
  const EVENT_TOTAL_COUNTS = 'event_total_counts';
  const EVENT_VALUE_COUNT = 'event_value_count';
  const HAD_PII = 'had_pii';
  const HOST = 'host';
  const MATCH_KEYS = 'match_keys';
  const PIXEL_FIRE = 'pixel_fire';
  const URL = 'url';
  const URL_BY_RULE = 'url_by_rule';
}
