<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static AdVideoUnpublishedContentTypeValues getInstance()
 */
class AdVideoUnpublishedContentTypeValues extends AbstractEnum {

  const ADS_POST = 'ADS_POST';
  const DRAFT = 'DRAFT';
  const INLINE_CREATED = 'INLINE_CREATED';
  const PUBLISHED = 'PUBLISHED';
  const REVIEWABLE_BRANDED_CONTENT = 'REVIEWABLE_BRANDED_CONTENT';
  const SCHEDULED = 'SCHEDULED';
  const SCHEDULED_RECURRING = 'SCHEDULED_RECURRING';
}
