<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CommerceOrderFiltersValues getInstance()
 */
class CommerceOrderFiltersValues extends AbstractEnum {

  const HAS_CANCELLATIONS = 'HAS_CANCELLATIONS';
  const HAS_FULFILLMENTS = 'HAS_FULFILLMENTS';
  const HAS_REFUNDS = 'HAS_REFUNDS';
  const NO_CANCELLATIONS = 'NO_CANCELLATIONS';
  const NO_REFUNDS = 'NO_REFUNDS';
  const NO_SHIPMENTS = 'NO_SHIPMENTS';
}
