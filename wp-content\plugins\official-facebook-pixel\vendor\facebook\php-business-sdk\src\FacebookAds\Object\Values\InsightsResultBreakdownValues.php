<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static InsightsResultBreakdownValues getInstance()
 */
class InsightsResultBreakdownValues extends AbstractEnum {

  const ACTION_TYPE = 'action_type';
  const FOLLOW_TYPE = 'follow_type';
  const STORY_NAVIGATION_ACTION_TYPE = 'story_navigation_action_type';
  const SURFACE_TYPE = 'surface_type';
}
