<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CampaignEffectiveStatusValues getInstance()
 */
class CampaignEffectiveStatusValues extends AbstractEnum {

  const ACTIVE = 'ACTIVE';
  const ARCHIVED = 'ARCHIVED';
  const DELETED = 'DELETED';
  const IN_PROCESS = 'IN_PROCESS';
  const PAUSED = 'PAUSED';
  const WITH_ISSUES = 'WITH_ISSUES';
}
