<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CommerceOrderReasonCodeValues getInstance()
 */
class CommerceOrderReasonCodeValues extends AbstractEnum {

  const BUYERS_REMORSE = 'BUYERS_REMORSE';
  const DAMAGED_GOODS = 'DAMAGED_GOODS';
  const FACEBOOK_INITIATED = 'FACEBOOK_INITIATED';
  const NOT_AS_DESCRIBED = 'NOT_AS_DESCRIBED';
  const QUALITY_ISSUE = 'QUALITY_ISSUE';
  const REFUND_COMPROMISED = 'REFUND_COMPROMISED';
  const REFUND_FOR_RETURN = 'REFUND_FOR_RETURN';
  const REFUND_REASON_OTHER = 'REFUND_REASON_OTHER';
  const REFUND_SFI_FAKE = 'REFUND_SFI_FAKE';
  const REFUND_SFI_REAL = 'REFUND_SFI_REAL';
  const WRONG_ITEM = 'WRONG_ITEM';
}
