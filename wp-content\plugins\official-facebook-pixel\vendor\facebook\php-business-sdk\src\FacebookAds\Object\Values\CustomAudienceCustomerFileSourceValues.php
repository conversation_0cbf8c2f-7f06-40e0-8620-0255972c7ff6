<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CustomAudienceCustomerFileSourceValues getInstance()
 */
class CustomAudienceCustomerFileSourceValues extends AbstractEnum {

  const BOTH_USER_AND_PARTNER_PROVIDED = 'BOTH_USER_AND_PARTNER_PROVIDED';
  const PARTNER_PROVIDED_ONLY = 'PARTNER_PROVIDED_ONLY';
  const USER_PROVIDED_ONLY = 'USER_PROVIDED_ONLY';
}
