<?php

// autoload_real.php @generated by Composer

class ComposerAutoloaderInitbcfa334bbed9b46e9f878d2ca61bc6a0
{
    private static $loader;

    public static function loadClassLoader($class)
    {
        if ('Composer\Autoload\ClassLoader' === $class) {
            require __DIR__ . '/ClassLoader.php';
        }
    }

    /**
     * @return \Composer\Autoload\ClassLoader
     */
    public static function getLoader()
    {
        if (null !== self::$loader) {
            return self::$loader;
        }

        require __DIR__ . '/platform_check.php';

        spl_autoload_register(array('ComposerAutoloaderInitbcfa334bbed9b46e9f878d2ca61bc6a0', 'loadClassLoader'), true, true);
        self::$loader = $loader = new \Composer\Autoload\ClassLoader(\dirname(__DIR__));
        spl_autoload_unregister(array('ComposerAutoloaderInitbcfa334bbed9b46e9f878d2ca61bc6a0', 'loadClassLoader'));

        require __DIR__ . '/autoload_static.php';
        call_user_func(\Composer\Autoload\ComposerStaticInitbcfa334bbed9b46e9f878d2ca61bc6a0::getInitializer($loader));

        $loader->register(true);

        return $loader;
    }
}
