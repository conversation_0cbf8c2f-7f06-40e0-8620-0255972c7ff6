<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Fields;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 */

class AdCreativeFeaturesSpecFields extends AbstractEnum {

  const ADAPT_TO_PLACEMENT = 'adapt_to_placement';
  const ADD_TEXT_OVERLAY = 'add_text_overlay';
  const ADS_WITH_BENEFITS = 'ads_with_benefits';
  const ADVANTAGE_PLUS_CREATIVE = 'advantage_plus_creative';
  const APP_HIGHLIGHTS = 'app_highlights';
  const AUDIO = 'audio';
  const CAROUSEL_TO_VIDEO = 'carousel_to_video';
  const CATALOG_FEED_TAG = 'catalog_feed_tag';
  const CUSTOMIZE_PRODUCT_RECOMMENDATION = 'customize_product_recommendation';
  const CV_TRANSFORMATION = 'cv_transformation';
  const DESCRIPTION_AUTOMATION = 'description_automation';
  const DHA_OPTIMIZATION = 'dha_optimization';
  const DYNAMIC_PARTNER_CONTENT = 'dynamic_partner_content';
  const ENHANCE_CTA = 'enhance_cta';
  const FB_FEED_TAG = 'fb_feed_tag';
  const FB_REELS_TAG = 'fb_reels_tag';
  const FB_STORY_TAG = 'fb_story_tag';
  const FEED_CAPTION_OPTIMIZATION = 'feed_caption_optimization';
  const IG_FEED_TAG = 'ig_feed_tag';
  const IG_GLADOS_FEED = 'ig_glados_feed';
  const IG_REELS_TAG = 'ig_reels_tag';
  const IG_STREAM_TAG = 'ig_stream_tag';
  const IMAGE_ANIMATION = 'image_animation';
  const IMAGE_AUTO_CROP = 'image_auto_crop';
  const IMAGE_BACKGROUND_GEN = 'image_background_gen';
  const IMAGE_BRIGHTNESS_AND_CONTRAST = 'image_brightness_and_contrast';
  const IMAGE_ENHANCEMENT = 'image_enhancement';
  const IMAGE_TEMPLATES = 'image_templates';
  const IMAGE_TOUCHUPS = 'image_touchups';
  const IMAGE_UNCROP = 'image_uncrop';
  const INLINE_COMMENT = 'inline_comment';
  const LOCAL_STORE_EXTENSION = 'local_store_extension';
  const MEDIA_LIQUIDITY_ANIMATED_IMAGE = 'media_liquidity_animated_image';
  const MEDIA_ORDER = 'media_order';
  const MEDIA_TYPE_AUTOMATION = 'media_type_automation';
  const MULTI_PHOTO_TO_VIDEO = 'multi_photo_to_video';
  const PAC_RELAXATION = 'pac_relaxation';
  const PRODUCT_EXTENSIONS = 'product_extensions';
  const PRODUCT_METADATA_AUTOMATION = 'product_metadata_automation';
  const PRODUCT_TAGS = 'product_tags';
  const PROFILE_CARD = 'profile_card';
  const SITE_EXTENSIONS = 'site_extensions';
  const STANDARD_ENHANCEMENTS = 'standard_enhancements';
  const STANDARD_ENHANCEMENTS_CATALOG = 'standard_enhancements_catalog';
  const TEXT_GENERATION = 'text_generation';
  const TEXT_OPTIMIZATIONS = 'text_optimizations';
  const VIDEO_AUTO_CROP = 'video_auto_crop';
  const VIDEO_FILTERING = 'video_filtering';
  const VIDEO_HIGHLIGHT = 'video_highlight';
  const VIDEO_UNCROP = 'video_uncrop';

  public function getFieldTypes() {
    return array(
      'adapt_to_placement' => 'AdCreativeFeatureDetails',
      'add_text_overlay' => 'AdCreativeFeatureDetails',
      'ads_with_benefits' => 'AdCreativeFeatureDetails',
      'advantage_plus_creative' => 'AdCreativeFeatureDetails',
      'app_highlights' => 'AdCreativeFeatureDetails',
      'audio' => 'AdCreativeFeatureDetails',
      'carousel_to_video' => 'AdCreativeFeatureDetails',
      'catalog_feed_tag' => 'AdCreativeFeatureDetails',
      'customize_product_recommendation' => 'AdCreativeFeatureDetails',
      'cv_transformation' => 'AdCreativeFeatureDetails',
      'description_automation' => 'AdCreativeFeatureDetails',
      'dha_optimization' => 'AdCreativeFeatureDetails',
      'dynamic_partner_content' => 'AdCreativeFeatureDetails',
      'enhance_cta' => 'AdCreativeFeatureDetails',
      'fb_feed_tag' => 'AdCreativeFeatureDetails',
      'fb_reels_tag' => 'AdCreativeFeatureDetails',
      'fb_story_tag' => 'AdCreativeFeatureDetails',
      'feed_caption_optimization' => 'AdCreativeFeatureDetails',
      'ig_feed_tag' => 'AdCreativeFeatureDetails',
      'ig_glados_feed' => 'AdCreativeFeatureDetails',
      'ig_reels_tag' => 'AdCreativeFeatureDetails',
      'ig_stream_tag' => 'AdCreativeFeatureDetails',
      'image_animation' => 'AdCreativeFeatureDetails',
      'image_auto_crop' => 'AdCreativeFeatureDetails',
      'image_background_gen' => 'AdCreativeFeatureDetails',
      'image_brightness_and_contrast' => 'AdCreativeFeatureDetails',
      'image_enhancement' => 'AdCreativeFeatureDetails',
      'image_templates' => 'AdCreativeFeatureDetails',
      'image_touchups' => 'AdCreativeFeatureDetails',
      'image_uncrop' => 'AdCreativeFeatureDetails',
      'inline_comment' => 'AdCreativeFeatureDetails',
      'local_store_extension' => 'AdCreativeFeatureDetails',
      'media_liquidity_animated_image' => 'AdCreativeFeatureDetails',
      'media_order' => 'AdCreativeFeatureDetails',
      'media_type_automation' => 'AdCreativeFeatureDetails',
      'multi_photo_to_video' => 'AdCreativeFeatureDetails',
      'pac_relaxation' => 'AdCreativeFeatureDetails',
      'product_extensions' => 'AdCreativeFeatureDetails',
      'product_metadata_automation' => 'AdCreativeFeatureDetails',
      'product_tags' => 'AdCreativeFeatureDetails',
      'profile_card' => 'AdCreativeFeatureDetails',
      'site_extensions' => 'AdCreativeFeatureDetails',
      'standard_enhancements' => 'AdCreativeFeatureDetails',
      'standard_enhancements_catalog' => 'AdCreativeFeatureDetails',
      'text_generation' => 'AdCreativeFeatureDetails',
      'text_optimizations' => 'AdCreativeFeatureDetails',
      'video_auto_crop' => 'AdCreativeFeatureDetails',
      'video_filtering' => 'AdCreativeFeatureDetails',
      'video_highlight' => 'AdCreativeFeatureDetails',
      'video_uncrop' => 'AdCreativeFeatureDetails',
    );
  }
}
