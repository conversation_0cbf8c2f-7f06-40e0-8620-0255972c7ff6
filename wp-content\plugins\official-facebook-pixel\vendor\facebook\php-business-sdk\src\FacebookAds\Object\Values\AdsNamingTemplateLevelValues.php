<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static AdsNamingTemplateLevelValues getInstance()
 */
class AdsNamingTemplateLevelValues extends AbstractEnum {

  const ADGROUP = 'ADGROUP';
  const AD_ACCOUNT = 'AD_ACCOUNT';
  const CAMPAIGN = 'CAMPAIGN';
  const CAMPAIGN_GROUP = 'CAMPAIGN_GROUP';
  const OPPORTUNITIES = 'OPPORTUNITIES';
  const PRIVACY_INFO_CENTER = 'PRIVACY_INFO_CENTER';
  const TOPLINE = 'TOPLINE';
  const UNIQUE_ADCREATIVE = 'UNIQUE_ADCREATIVE';
}
