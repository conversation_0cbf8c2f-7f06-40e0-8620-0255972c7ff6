<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CommentCommentPrivacyValueValues getInstance()
 */
class CommentCommentPrivacyValueValues extends AbstractEnum {

  const DECLINED_BY_ADMIN_ASSISTANT = 'DECLINED_BY_ADMIN_ASSISTANT';
  const DEFAULT_PRIVACY = 'DEFAULT_PRIVACY';
  const FRIENDS_AND_POST_OWNER = 'FRIENDS_AND_POST_OWNER';
  const FRIENDS_ONLY = 'FRIENDS_ONLY';
  const GRAPHQL_MULTIPLE_VALUE_HACK_DO_NOT_USE = 'GRAPHQL_MULTIPLE_VALUE_HACK_DO_NOT_USE';
  const OWNER_OR_COMMENTER = 'OWNER_OR_COMMENTER';
  const PENDING_APPROVAL = 'PENDING_APPROVAL';
  const REMOVED_BY_ADMIN_ASSISTANT = 'REMOVED_BY_ADMIN_ASSISTANT';
  const SIDE_CONVERSATION = 'SIDE_CONVERSATION';
  const SIDE_CONVERSATION_AND_POST_OWNER = 'SIDE_CONVERSATION_AND_POST_OWNER';
  const SPOTLIGHT_TAB = 'SPOTLIGHT_TAB';
}
