<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static AdsPixelAutomaticMatchingFieldsValues getInstance()
 */
class AdsPixelAutomaticMatchingFieldsValues extends AbstractEnum {

  const COUNTRY = 'country';
  const CT = 'ct';
  const DB = 'db';
  const EM = 'em';
  const EXTERNAL_ID = 'external_id';
  const FN = 'fn';
  const GE = 'ge';
  const LN = 'ln';
  const PH = 'ph';
  const ST = 'st';
  const ZP = 'zp';
}
