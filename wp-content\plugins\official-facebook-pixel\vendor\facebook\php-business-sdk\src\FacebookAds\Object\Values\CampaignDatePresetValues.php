<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CampaignDatePresetValues getInstance()
 */
class CampaignDatePresetValues extends AbstractEnum {

  const DATA_MAXIMUM = 'data_maximum';
  const LAST_14D = 'last_14d';
  const LAST_28D = 'last_28d';
  const LAST_30D = 'last_30d';
  const LAST_3D = 'last_3d';
  const LAST_7D = 'last_7d';
  const LAST_90D = 'last_90d';
  const LAST_MONTH = 'last_month';
  const LAST_QUARTER = 'last_quarter';
  const LAST_WEEK_MON_SUN = 'last_week_mon_sun';
  const LAST_WEEK_SUN_SAT = 'last_week_sun_sat';
  const LAST_YEAR = 'last_year';
  const MAXIMUM = 'maximum';
  const THIS_MONTH = 'this_month';
  const THIS_QUARTER = 'this_quarter';
  const THIS_WEEK_MON_TODAY = 'this_week_mon_today';
  const THIS_WEEK_SUN_TODAY = 'this_week_sun_today';
  const THIS_YEAR = 'this_year';
  const TODAY = 'today';
  const YESTERDAY = 'yesterday';
}
