<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CampaignSpecialAdCategoryCountryValues getInstance()
 */
class CampaignSpecialAdCategoryCountryValues extends AbstractEnum {

  const AD = 'AD';
  const AE = 'AE';
  const AF = 'AF';
  const AG = 'AG';
  const AI = 'AI';
  const AL = 'AL';
  const AM = 'AM';
  const AN = 'AN';
  const AO = 'AO';
  const AQ = 'AQ';
  const AR = 'AR';
  const VALUE_AS = 'AS';
  const AT = 'AT';
  const AU = 'AU';
  const AW = 'AW';
  const AX = 'AX';
  const AZ = 'AZ';
  const BA = 'BA';
  const BB = 'BB';
  const BD = 'BD';
  const BE = 'BE';
  const BF = 'BF';
  const BG = 'BG';
  const BH = 'BH';
  const BI = 'BI';
  const BJ = 'BJ';
  const BL = 'BL';
  const BM = 'BM';
  const BN = 'BN';
  const BO = 'BO';
  const BQ = 'BQ';
  const BR = 'BR';
  const BS = 'BS';
  const BT = 'BT';
  const BV = 'BV';
  const BW = 'BW';
  const BY = 'BY';
  const BZ = 'BZ';
  const CA = 'CA';
  const CC = 'CC';
  const CD = 'CD';
  const CF = 'CF';
  const CG = 'CG';
  const CH = 'CH';
  const CI = 'CI';
  const CK = 'CK';
  const CL = 'CL';
  const CM = 'CM';
  const CN = 'CN';
  const CO = 'CO';
  const CR = 'CR';
  const CU = 'CU';
  const CV = 'CV';
  const CW = 'CW';
  const CX = 'CX';
  const CY = 'CY';
  const CZ = 'CZ';
  const DE = 'DE';
  const DJ = 'DJ';
  const DK = 'DK';
  const DM = 'DM';
  const VALUE_DO = 'DO';
  const DZ = 'DZ';
  const EC = 'EC';
  const EE = 'EE';
  const EG = 'EG';
  const EH = 'EH';
  const ER = 'ER';
  const ES = 'ES';
  const ET = 'ET';
  const FI = 'FI';
  const FJ = 'FJ';
  const FK = 'FK';
  const FM = 'FM';
  const FO = 'FO';
  const FR = 'FR';
  const GA = 'GA';
  const GB = 'GB';
  const GD = 'GD';
  const GE = 'GE';
  const GF = 'GF';
  const GG = 'GG';
  const GH = 'GH';
  const GI = 'GI';
  const GL = 'GL';
  const GM = 'GM';
  const GN = 'GN';
  const GP = 'GP';
  const GQ = 'GQ';
  const GR = 'GR';
  const GS = 'GS';
  const GT = 'GT';
  const GU = 'GU';
  const GW = 'GW';
  const GY = 'GY';
  const HK = 'HK';
  const HM = 'HM';
  const HN = 'HN';
  const HR = 'HR';
  const HT = 'HT';
  const HU = 'HU';
  const ID = 'ID';
  const IE = 'IE';
  const IL = 'IL';
  const IM = 'IM';
  const IN = 'IN';
  const IO = 'IO';
  const IQ = 'IQ';
  const IR = 'IR';
  const IS = 'IS';
  const IT = 'IT';
  const JE = 'JE';
  const JM = 'JM';
  const JO = 'JO';
  const JP = 'JP';
  const KE = 'KE';
  const KG = 'KG';
  const KH = 'KH';
  const KI = 'KI';
  const KM = 'KM';
  const KN = 'KN';
  const KP = 'KP';
  const KR = 'KR';
  const KW = 'KW';
  const KY = 'KY';
  const KZ = 'KZ';
  const LA = 'LA';
  const LB = 'LB';
  const LC = 'LC';
  const LI = 'LI';
  const LK = 'LK';
  const LR = 'LR';
  const LS = 'LS';
  const LT = 'LT';
  const LU = 'LU';
  const LV = 'LV';
  const LY = 'LY';
  const MA = 'MA';
  const MC = 'MC';
  const MD = 'MD';
  const ME = 'ME';
  const MF = 'MF';
  const MG = 'MG';
  const MH = 'MH';
  const MK = 'MK';
  const ML = 'ML';
  const MM = 'MM';
  const MN = 'MN';
  const MO = 'MO';
  const MP = 'MP';
  const MQ = 'MQ';
  const MR = 'MR';
  const MS = 'MS';
  const MT = 'MT';
  const MU = 'MU';
  const MV = 'MV';
  const MW = 'MW';
  const MX = 'MX';
  const MY = 'MY';
  const MZ = 'MZ';
  const NA = 'NA';
  const NC = 'NC';
  const NE = 'NE';
  const NF = 'NF';
  const NG = 'NG';
  const NI = 'NI';
  const NL = 'NL';
  const NO = 'NO';
  const NP = 'NP';
  const NR = 'NR';
  const NU = 'NU';
  const NZ = 'NZ';
  const OM = 'OM';
  const PA = 'PA';
  const PE = 'PE';
  const PF = 'PF';
  const PG = 'PG';
  const PH = 'PH';
  const PK = 'PK';
  const PL = 'PL';
  const PM = 'PM';
  const PN = 'PN';
  const PR = 'PR';
  const PS = 'PS';
  const PT = 'PT';
  const PW = 'PW';
  const PY = 'PY';
  const QA = 'QA';
  const RE = 'RE';
  const RO = 'RO';
  const RS = 'RS';
  const RU = 'RU';
  const RW = 'RW';
  const SA = 'SA';
  const SB = 'SB';
  const SC = 'SC';
  const SD = 'SD';
  const SE = 'SE';
  const SG = 'SG';
  const SH = 'SH';
  const SI = 'SI';
  const SJ = 'SJ';
  const SK = 'SK';
  const SL = 'SL';
  const SM = 'SM';
  const SN = 'SN';
  const SO = 'SO';
  const SR = 'SR';
  const SS = 'SS';
  const ST = 'ST';
  const SV = 'SV';
  const SX = 'SX';
  const SY = 'SY';
  const SZ = 'SZ';
  const TC = 'TC';
  const TD = 'TD';
  const TF = 'TF';
  const TG = 'TG';
  const TH = 'TH';
  const TJ = 'TJ';
  const TK = 'TK';
  const TL = 'TL';
  const TM = 'TM';
  const TN = 'TN';
  const TO = 'TO';
  const TR = 'TR';
  const TT = 'TT';
  const TV = 'TV';
  const TW = 'TW';
  const TZ = 'TZ';
  const UA = 'UA';
  const UG = 'UG';
  const UM = 'UM';
  const US = 'US';
  const UY = 'UY';
  const UZ = 'UZ';
  const VA = 'VA';
  const VC = 'VC';
  const VE = 'VE';
  const VG = 'VG';
  const VI = 'VI';
  const VN = 'VN';
  const VU = 'VU';
  const WF = 'WF';
  const WS = 'WS';
  const XK = 'XK';
  const YE = 'YE';
  const YT = 'YT';
  const ZA = 'ZA';
  const ZM = 'ZM';
  const ZW = 'ZW';
}
