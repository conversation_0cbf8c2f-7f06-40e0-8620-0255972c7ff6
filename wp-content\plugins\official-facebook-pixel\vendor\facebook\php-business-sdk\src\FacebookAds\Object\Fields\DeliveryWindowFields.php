<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Fields;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 */

class DeliveryWindowFields extends AbstractEnum {

  const AD = 'ad';
  const AE = 'ae';
  const AF = 'af';
  const AG = 'ag';
  const AI = 'ai';
  const AL = 'al';
  const ALL = 'all';
  const AM = 'am';
  const AN = 'an';
  const AO = 'ao';
  const AQ = 'aq';
  const AR = 'ar';
  const FIELD_AS = 'as';
  const AT = 'at';
  const AU = 'au';
  const AW = 'aw';
  const AX = 'ax';
  const AZ = 'az';
  const BA = 'ba';
  const BB = 'bb';
  const BD = 'bd';
  const BE = 'be';
  const BF = 'bf';
  const BG = 'bg';
  const BH = 'bh';
  const BI = 'bi';
  const BJ = 'bj';
  const BL = 'bl';
  const BM = 'bm';
  const BN = 'bn';
  const BO = 'bo';
  const BQ = 'bq';
  const BR = 'br';
  const BS = 'bs';
  const BT = 'bt';
  const BV = 'bv';
  const BW = 'bw';
  const BY = 'by';
  const BZ = 'bz';
  const CA = 'ca';
  const CC = 'cc';
  const CD = 'cd';
  const CF = 'cf';
  const CG = 'cg';
  const CH = 'ch';
  const CI = 'ci';
  const CK = 'ck';
  const CL = 'cl';
  const CM = 'cm';
  const CN = 'cn';
  const CO = 'co';
  const CR = 'cr';
  const CU = 'cu';
  const CV = 'cv';
  const CW = 'cw';
  const CX = 'cx';
  const CY = 'cy';
  const CZ = 'cz';
  const DE = 'de';
  const DJ = 'dj';
  const DK = 'dk';
  const DM = 'dm';
  const FIELD_DO = 'do';
  const DZ = 'dz';
  const EC = 'ec';
  const EE = 'ee';
  const EG = 'eg';
  const EH = 'eh';
  const ER = 'er';
  const ES = 'es';
  const ET = 'et';
  const FI = 'fi';
  const FJ = 'fj';
  const FK = 'fk';
  const FM = 'fm';
  const FO = 'fo';
  const FR = 'fr';
  const GA = 'ga';
  const GB = 'gb';
  const GD = 'gd';
  const GE = 'ge';
  const GF = 'gf';
  const GG = 'gg';
  const GH = 'gh';
  const GI = 'gi';
  const GL = 'gl';
  const GM = 'gm';
  const GN = 'gn';
  const GP = 'gp';
  const GQ = 'gq';
  const GR = 'gr';
  const GS = 'gs';
  const GT = 'gt';
  const GU = 'gu';
  const GW = 'gw';
  const GY = 'gy';
  const HK = 'hk';
  const HM = 'hm';
  const HN = 'hn';
  const HR = 'hr';
  const HT = 'ht';
  const HU = 'hu';
  const ID = 'id';
  const IE = 'ie';
  const IL = 'il';
  const IM = 'im';
  const IN = 'in';
  const IO = 'io';
  const IQ = 'iq';
  const IR = 'ir';
  const IS = 'is';
  const IT = 'it';
  const JE = 'je';
  const JM = 'jm';
  const JO = 'jo';
  const JP = 'jp';
  const KE = 'ke';
  const KG = 'kg';
  const KH = 'kh';
  const KI = 'ki';
  const KM = 'km';
  const KN = 'kn';
  const KP = 'kp';
  const KR = 'kr';
  const KW = 'kw';
  const KY = 'ky';
  const KZ = 'kz';
  const LA = 'la';
  const LB = 'lb';
  const LC = 'lc';
  const LI = 'li';
  const LK = 'lk';
  const LR = 'lr';
  const LS = 'ls';
  const LT = 'lt';
  const LU = 'lu';
  const LV = 'lv';
  const LY = 'ly';
  const MA = 'ma';
  const MC = 'mc';
  const MD = 'md';
  const ME = 'me';
  const MF = 'mf';
  const MG = 'mg';
  const MH = 'mh';
  const MK = 'mk';
  const ML = 'ml';
  const MM = 'mm';
  const MN = 'mn';
  const MO = 'mo';
  const MP = 'mp';
  const MQ = 'mq';
  const MR = 'mr';
  const MS = 'ms';
  const MT = 'mt';
  const MU = 'mu';
  const MV = 'mv';
  const MW = 'mw';
  const MX = 'mx';
  const MY = 'my';
  const MZ = 'mz';
  const NA = 'na';
  const NC = 'nc';
  const NE = 'ne';
  const NF = 'nf';
  const NG = 'ng';
  const NI = 'ni';
  const NL = 'nl';
  const NO = 'no';
  const NP = 'np';
  const NR = 'nr';
  const NU = 'nu';
  const NZ = 'nz';
  const OM = 'om';
  const PA = 'pa';
  const PE = 'pe';
  const PF = 'pf';
  const PG = 'pg';
  const PH = 'ph';
  const PK = 'pk';
  const PL = 'pl';
  const PM = 'pm';
  const PN = 'pn';
  const PR = 'pr';
  const PS = 'ps';
  const PT = 'pt';
  const PW = 'pw';
  const PY = 'py';
  const QA = 'qa';
  const RE = 're';
  const RO = 'ro';
  const RS = 'rs';
  const RU = 'ru';
  const RW = 'rw';
  const SA = 'sa';
  const SB = 'sb';
  const SC = 'sc';
  const SD = 'sd';
  const SE = 'se';
  const SG = 'sg';
  const SH = 'sh';
  const SI = 'si';
  const SJ = 'sj';
  const SK = 'sk';
  const SL = 'sl';
  const SM = 'sm';
  const SN = 'sn';
  const SO = 'so';
  const SR = 'sr';
  const SS = 'ss';
  const ST = 'st';
  const SV = 'sv';
  const SX = 'sx';
  const SY = 'sy';
  const SZ = 'sz';
  const TC = 'tc';
  const TD = 'td';
  const TF = 'tf';
  const TG = 'tg';
  const TH = 'th';
  const TJ = 'tj';
  const TK = 'tk';
  const TL = 'tl';
  const TM = 'tm';
  const TN = 'tn';
  const TO = 'to';
  const TR = 'tr';
  const TT = 'tt';
  const TV = 'tv';
  const TW = 'tw';
  const TZ = 'tz';
  const UA = 'ua';
  const UG = 'ug';
  const UM = 'um';
  const US = 'us';
  const UY = 'uy';
  const UZ = 'uz';
  const VA = 'va';
  const VC = 'vc';
  const VE = 've';
  const VG = 'vg';
  const VI = 'vi';
  const VN = 'vn';
  const VU = 'vu';
  const WF = 'wf';
  const WS = 'ws';
  const XK = 'xk';
  const YE = 'ye';
  const YT = 'yt';
  const ZA = 'za';
  const ZM = 'zm';
  const ZW = 'zw';

  public function getFieldTypes() {
    return array(
      'ad' => 'int',
      'ae' => 'int',
      'af' => 'int',
      'ag' => 'int',
      'ai' => 'int',
      'al' => 'int',
      'all' => 'int',
      'am' => 'int',
      'an' => 'int',
      'ao' => 'int',
      'aq' => 'int',
      'ar' => 'int',
      'as' => 'int',
      'at' => 'int',
      'au' => 'int',
      'aw' => 'int',
      'ax' => 'int',
      'az' => 'int',
      'ba' => 'int',
      'bb' => 'int',
      'bd' => 'int',
      'be' => 'int',
      'bf' => 'int',
      'bg' => 'int',
      'bh' => 'int',
      'bi' => 'int',
      'bj' => 'int',
      'bl' => 'int',
      'bm' => 'int',
      'bn' => 'int',
      'bo' => 'int',
      'bq' => 'int',
      'br' => 'int',
      'bs' => 'int',
      'bt' => 'int',
      'bv' => 'int',
      'bw' => 'int',
      'by' => 'int',
      'bz' => 'int',
      'ca' => 'int',
      'cc' => 'int',
      'cd' => 'int',
      'cf' => 'int',
      'cg' => 'int',
      'ch' => 'int',
      'ci' => 'int',
      'ck' => 'int',
      'cl' => 'int',
      'cm' => 'int',
      'cn' => 'int',
      'co' => 'int',
      'cr' => 'int',
      'cu' => 'int',
      'cv' => 'int',
      'cw' => 'int',
      'cx' => 'int',
      'cy' => 'int',
      'cz' => 'int',
      'de' => 'int',
      'dj' => 'int',
      'dk' => 'int',
      'dm' => 'int',
      'do' => 'int',
      'dz' => 'int',
      'ec' => 'int',
      'ee' => 'int',
      'eg' => 'int',
      'eh' => 'int',
      'er' => 'int',
      'es' => 'int',
      'et' => 'int',
      'fi' => 'int',
      'fj' => 'int',
      'fk' => 'int',
      'fm' => 'int',
      'fo' => 'int',
      'fr' => 'int',
      'ga' => 'int',
      'gb' => 'int',
      'gd' => 'int',
      'ge' => 'int',
      'gf' => 'int',
      'gg' => 'int',
      'gh' => 'int',
      'gi' => 'int',
      'gl' => 'int',
      'gm' => 'int',
      'gn' => 'int',
      'gp' => 'int',
      'gq' => 'int',
      'gr' => 'int',
      'gs' => 'int',
      'gt' => 'int',
      'gu' => 'int',
      'gw' => 'int',
      'gy' => 'int',
      'hk' => 'int',
      'hm' => 'int',
      'hn' => 'int',
      'hr' => 'int',
      'ht' => 'int',
      'hu' => 'int',
      'id' => 'int',
      'ie' => 'int',
      'il' => 'int',
      'im' => 'int',
      'in' => 'int',
      'io' => 'int',
      'iq' => 'int',
      'ir' => 'int',
      'is' => 'int',
      'it' => 'int',
      'je' => 'int',
      'jm' => 'int',
      'jo' => 'int',
      'jp' => 'int',
      'ke' => 'int',
      'kg' => 'int',
      'kh' => 'int',
      'ki' => 'int',
      'km' => 'int',
      'kn' => 'int',
      'kp' => 'int',
      'kr' => 'int',
      'kw' => 'int',
      'ky' => 'int',
      'kz' => 'int',
      'la' => 'int',
      'lb' => 'int',
      'lc' => 'int',
      'li' => 'int',
      'lk' => 'int',
      'lr' => 'int',
      'ls' => 'int',
      'lt' => 'int',
      'lu' => 'int',
      'lv' => 'int',
      'ly' => 'int',
      'ma' => 'int',
      'mc' => 'int',
      'md' => 'int',
      'me' => 'int',
      'mf' => 'int',
      'mg' => 'int',
      'mh' => 'int',
      'mk' => 'int',
      'ml' => 'int',
      'mm' => 'int',
      'mn' => 'int',
      'mo' => 'int',
      'mp' => 'int',
      'mq' => 'int',
      'mr' => 'int',
      'ms' => 'int',
      'mt' => 'int',
      'mu' => 'int',
      'mv' => 'int',
      'mw' => 'int',
      'mx' => 'int',
      'my' => 'int',
      'mz' => 'int',
      'na' => 'int',
      'nc' => 'int',
      'ne' => 'int',
      'nf' => 'int',
      'ng' => 'int',
      'ni' => 'int',
      'nl' => 'int',
      'no' => 'int',
      'np' => 'int',
      'nr' => 'int',
      'nu' => 'int',
      'nz' => 'int',
      'om' => 'int',
      'pa' => 'int',
      'pe' => 'int',
      'pf' => 'int',
      'pg' => 'int',
      'ph' => 'int',
      'pk' => 'int',
      'pl' => 'int',
      'pm' => 'int',
      'pn' => 'int',
      'pr' => 'int',
      'ps' => 'int',
      'pt' => 'int',
      'pw' => 'int',
      'py' => 'int',
      'qa' => 'int',
      're' => 'int',
      'ro' => 'int',
      'rs' => 'int',
      'ru' => 'int',
      'rw' => 'int',
      'sa' => 'int',
      'sb' => 'int',
      'sc' => 'int',
      'sd' => 'int',
      'se' => 'int',
      'sg' => 'int',
      'sh' => 'int',
      'si' => 'int',
      'sj' => 'int',
      'sk' => 'int',
      'sl' => 'int',
      'sm' => 'int',
      'sn' => 'int',
      'so' => 'int',
      'sr' => 'int',
      'ss' => 'int',
      'st' => 'int',
      'sv' => 'int',
      'sx' => 'int',
      'sy' => 'int',
      'sz' => 'int',
      'tc' => 'int',
      'td' => 'int',
      'tf' => 'int',
      'tg' => 'int',
      'th' => 'int',
      'tj' => 'int',
      'tk' => 'int',
      'tl' => 'int',
      'tm' => 'int',
      'tn' => 'int',
      'to' => 'int',
      'tr' => 'int',
      'tt' => 'int',
      'tv' => 'int',
      'tw' => 'int',
      'tz' => 'int',
      'ua' => 'int',
      'ug' => 'int',
      'um' => 'int',
      'us' => 'int',
      'uy' => 'int',
      'uz' => 'int',
      'va' => 'int',
      'vc' => 'int',
      've' => 'int',
      'vg' => 'int',
      'vi' => 'int',
      'vn' => 'int',
      'vu' => 'int',
      'wf' => 'int',
      'ws' => 'int',
      'xk' => 'int',
      'ye' => 'int',
      'yt' => 'int',
      'za' => 'int',
      'zm' => 'int',
      'zw' => 'int',
    );
  }
}
