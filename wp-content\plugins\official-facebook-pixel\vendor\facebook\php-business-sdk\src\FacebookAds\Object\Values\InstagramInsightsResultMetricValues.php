<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static InstagramInsightsResultMetricValues getInstance()
 */
class InstagramInsightsResultMetricValues extends AbstractEnum {

  const CLIPS_REPLAYS_COUNT = 'clips_replays_count';
  const COMMENTS = 'comments';
  const FOLLOWS = 'follows';
  const IG_REELS_AGGREGATED_ALL_PLAYS_COUNT = 'ig_reels_aggregated_all_plays_count';
  const IG_REELS_AVG_WATCH_TIME = 'ig_reels_avg_watch_time';
  const IG_REELS_VIDEO_VIEW_TOTAL_TIME = 'ig_reels_video_view_total_time';
  const IMPRESSIONS = 'impressions';
  const LIKES = 'likes';
  const NAVIGATION = 'navigation';
  const PLAYS = 'plays';
  const PROFILE_ACTIVITY = 'profile_activity';
  const PROFILE_VISITS = 'profile_visits';
  const REACH = 'reach';
  const REPLIES = 'replies';
  const SAVED = 'saved';
  const SHARES = 'shares';
  const TOTAL_INTERACTIONS = 'total_interactions';
  const VIDEO_VIEWS = 'video_views';
  const VIEWS = 'views';
}
