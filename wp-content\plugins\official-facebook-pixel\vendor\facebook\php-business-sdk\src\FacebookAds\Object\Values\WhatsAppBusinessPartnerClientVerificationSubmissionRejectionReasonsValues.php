<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static WhatsAppBusinessPartnerClientVerificationSubmissionRejectionReasonsValues getInstance()
 */
class WhatsAppBusinessPartnerClientVerificationSubmissionRejectionReasonsValues extends AbstractEnum {

  const ADDRESS_NOT_MATCHING = 'ADDRESS_NOT_MATCHING';
  const BUSINESS_NOT_ELIGIBLE = 'BUSINESS_NOT_ELIGIBLE';
  const LEGAL_NAME_NOT_FOUND_IN_DOCUMENTS = 'LEGAL_NAME_NOT_FOUND_IN_DOCUMENTS';
  const LEGAL_NAME_NOT_MATCHING = 'LEGAL_NAME_NOT_MATCHING';
  const MALFORMED_DOCUMENTS = 'MALFORMED_DOCUMENTS';
  const NONE = 'NONE';
  const WEBSITE_NOT_MATCHING = 'WEBSITE_NOT_MATCHING';
}
