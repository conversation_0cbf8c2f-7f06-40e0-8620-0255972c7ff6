<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static InsightsResultTimeframeValues getInstance()
 */
class InsightsResultTimeframeValues extends AbstractEnum {

  const LAST_14_DAYS = 'last_14_days';
  const LAST_30_DAYS = 'last_30_days';
  const LAST_90_DAYS = 'last_90_days';
  const PREV_MONTH = 'prev_month';
  const THIS_MONTH = 'this_month';
  const THIS_WEEK = 'this_week';
}
