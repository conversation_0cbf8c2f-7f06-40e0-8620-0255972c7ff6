<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CampaignObjectiveValues getInstance()
 */
class CampaignObjectiveValues extends AbstractEnum {

  const APP_INSTALLS = 'APP_INSTALLS';
  const BRAND_AWARENESS = 'BRAND_AWARENESS';
  const CONVERSIONS = 'CONVERSIONS';
  const EVENT_RESPONSES = 'EVENT_RESPONSES';
  const LEAD_GENERATION = 'LEAD_GENERATION';
  const LINK_CLICKS = 'LINK_CLICKS';
  const LOCAL_AWARENESS = 'LOCAL_AWARENESS';
  const MESSAGES = 'MESSAGES';
  const OFFER_CLAIMS = 'OFFER_CLAIMS';
  const OUTCOME_APP_PROMOTION = 'OUTCOME_APP_PROMOTION';
  const OUTCOME_AWARENESS = 'OUTCOME_AWARENESS';
  const OUTCOME_ENGAGEMENT = 'OUTCOME_ENGAGEMENT';
  const OUTCOME_LEADS = 'OUTCOME_LEADS';
  const OUTCOME_SALES = 'OUTCOME_SALES';
  const OUTCOME_TRAFFIC = 'OUTCOME_TRAFFIC';
  const PAGE_LIKES = 'PAGE_LIKES';
  const POST_ENGAGEMENT = 'POST_ENGAGEMENT';
  const PRODUCT_CATALOG_SALES = 'PRODUCT_CATALOG_SALES';
  const REACH = 'REACH';
  const STORE_VISITS = 'STORE_VISITS';
  const VIDEO_VIEWS = 'VIDEO_VIEWS';
}
