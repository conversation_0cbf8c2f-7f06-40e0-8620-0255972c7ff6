<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CustomConversionCustomEventTypeValues getInstance()
 */
class CustomConversionCustomEventTypeValues extends AbstractEnum {

  const ADD_PAYMENT_INFO = 'ADD_PAYMENT_INFO';
  const ADD_TO_CART = 'ADD_TO_CART';
  const ADD_TO_WISHLIST = 'ADD_TO_WISHLIST';
  const COMPLETE_REGISTRATION = 'COMPLETE_REGISTRATION';
  const CONTACT = 'CONTACT';
  const CONTENT_VIEW = 'CONTENT_VIEW';
  const CUSTOMIZE_PRODUCT = 'CUSTOMIZE_PRODUCT';
  const DONATE = 'DONATE';
  const FACEBOOK_SELECTED = 'FACEBOOK_SELECTED';
  const FIND_LOCATION = 'FIND_LOCATION';
  const INITIATED_CHECKOUT = 'INITIATED_CHECKOUT';
  const LEAD = 'LEAD';
  const LISTING_INTERACTION = 'LISTING_INTERACTION';
  const OTHER = 'OTHER';
  const PURCHASE = 'PURCHASE';
  const SCHEDULE = 'SCHEDULE';
  const SEARCH = 'SEARCH';
  const START_TRIAL = 'START_TRIAL';
  const SUBMIT_APPLICATION = 'SUBMIT_APPLICATION';
  const SUBSCRIBE = 'SUBSCRIBE';
}
