<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static ApplicationSupportedPlatformsValues getInstance()
 */
class ApplicationSupportedPlatformsValues extends AbstractEnum {

  const AMAZON = 'AMAZON';
  const ANDROID = 'ANDROID';
  const CANVAS = 'CANVAS';
  const GAMEROOM = 'GAMEROOM';
  const INSTANT_GAME = 'INSTANT_GAME';
  const IPAD = 'IPAD';
  const IPHONE = 'IPHONE';
  const MOBILE_WEB = 'MOBILE_WEB';
  const OCULUS = 'OCULUS';
  const SAMSUNG = 'SAMSUNG';
  const SUPPLEMENTARY_IMAGES = 'SUPPLEMENTARY_IMAGES';
  const WEB = 'WEB';
  const WINDOWS = 'WINDOWS';
  const XIAOMI = 'XIAOMI';
}
