<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static ApplicationAnPlatformsValues getInstance()
 */
class ApplicationAnPlatformsValues extends AbstractEnum {

  const ANDROID = 'ANDROID';
  const DESKTOP = 'DESKTOP';
  const GALAXY = 'GALAXY';
  const INSTANT_ARTICLES = 'INSTANT_ARTICLES';
  const IOS = 'IOS';
  const MOBILE_WEB = 'MOBILE_WEB';
  const OCULUS = 'OCULUS';
  const UNKNOWN = 'UNKNOWN';
  const XIAOMI = 'XIAOMI';
}
