<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static VideoPollActionValues getInstance()
 */
class VideoPollActionValues extends AbstractEnum {

  const ATTACH_TO_VIDEO = 'ATTACH_TO_VIDEO';
  const CLOSE = 'CLOSE';
  const DELETE_POLL = 'DELETE_POLL';
  const SHOW_RESULTS = 'SHOW_RESULTS';
  const SHOW_VOTING = 'SHOW_VOTING';
}
