<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * @method static InsightsIncrements getInstance()
 */
class InsightsIncrements extends AbstractEnum {

  const MONTHLY = 'monthly';
  const ALL_DAYS = 'all_days';
}
