<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static BusinessVerificationStatusValues getInstance()
 */
class BusinessVerificationStatusValues extends AbstractEnum {

  const EXPIRED = 'expired';
  const FAILED = 'failed';
  const INELIGIBLE = 'ineligible';
  const NOT_VERIFIED = 'not_verified';
  const PENDING = 'pending';
  const PENDING_NEED_MORE_INFO = 'pending_need_more_info';
  const PENDING_SUBMISSION = 'pending_submission';
  const REJECTED = 'rejected';
  const REVOKED = 'revoked';
  const VERIFIED = 'verified';
}
