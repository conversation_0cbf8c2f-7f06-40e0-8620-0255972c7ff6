<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Search;

use FacebookAds\Enum\AbstractEnum;

/**
 * @method static DemographicSearchClasses getInstance()
 */
class DemographicSearchClasses extends AbstractEnum {

  const LIFE_EVENTS = 'life_events';
  const POLITICS = 'politics';
  const MARKETS = 'markets';
  const INDUSTRIES = 'industries';
  const INCOME = 'income';
  const NET_WORTH = 'net_worth';
  const HOME_TYPE = 'home_type';
  const HOME_OWNERSHIP = 'home_ownership';
  const HOME_VALUE = 'home_value';
  const ETHNIC_AFFINITY = 'ethnic_affinity';
  const GENERATION = 'generation';
  const HOUSEHOLD_COMPOSITION = 'household_composition';
  const MOMS = 'moms';
  const OFFICE_TYPE = 'office_type';
  const FAMILY_STATUSES = 'family_statuses';
  const DEMOGRAPHICS = 'demographics';
}
