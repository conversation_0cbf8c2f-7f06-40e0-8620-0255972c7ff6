<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static IGUpcomingEventNotificationSubtypesValues getInstance()
 */
class IGUpcomingEventNotificationSubtypesValues extends AbstractEnum {

  const AFTER_EVENT_1DAY = 'AFTER_EVENT_1DAY';
  const AFTER_EVENT_2DAY = 'AFTER_EVENT_2DAY';
  const AFTER_EVENT_3DAY = 'AFTER_EVENT_3DAY';
  const AFTER_EVENT_4DAY = 'AFTER_EVENT_4DAY';
  const AFTER_EVENT_5DAY = 'AFTER_EVENT_5DAY';
  const AFTER_EVENT_6DAY = 'AFTER_EVENT_6DAY';
  const AFTER_EVENT_7DAY = 'AFTER_EVENT_7DAY';
  const BEFORE_EVENT_15MIN = 'BEFORE_EVENT_15MIN';
  const BEFORE_EVENT_1DAY = 'BEFORE_EVENT_1DAY';
  const BEFORE_EVENT_1HOUR = 'BEFORE_EVENT_1HOUR';
  const BEFORE_EVENT_2DAY = 'BEFORE_EVENT_2DAY';
  const EVENT_START = 'EVENT_START';
  const RESCHEDULED = 'RESCHEDULED';
}
