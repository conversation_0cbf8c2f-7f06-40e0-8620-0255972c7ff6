<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CustomAudienceContentTypeValues getInstance()
 */
class CustomAudienceContentTypeValues extends AbstractEnum {

  const AUTOMOTIVE_MODEL = 'AUTOMOTIVE_MODEL';
  const DESTINATION = 'DESTINATION';
  const FLIGHT = 'FLIGHT';
  const GENERIC = 'GENERIC';
  const HOME_LISTING = 'HOME_LISTING';
  const HOTEL = 'HOTEL';
  const JOB = 'JOB';
  const LOCAL_SERVICE_BUSINESS = 'LOCAL_SERVICE_BUSINESS';
  const MEDIA_TITLE = 'MEDIA_TITLE';
  const OFFLINE_PRODUCT = 'OFFLINE_PRODUCT';
  const PRODUCT = 'PRODUCT';
  const VEHICLE = 'VEHICLE';
  const VEHICLE_OFFER = 'VEHICLE_OFFER';
}
