<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CustomAudienceDataSourceSubTypeValues getInstance()
 */
class CustomAudienceDataSourceSubTypeValues extends AbstractEnum {

  const AD_CAMPAIGN = 'AD_CAMPAIGN';
  const ANYTHING = 'ANYTHING';
  const APP_USERS = 'APP_USERS';
  const AR_EFFECTS_EVENTS = 'AR_EFFECTS_EVENTS';
  const AR_EXPERIENCE_EVENTS = 'AR_EXPERIENCE_EVENTS';
  const CAMPAIGN_CONVERSIONS = 'CAMPAIGN_CONVERSIONS';
  const COMBINATION_CUSTOM_AUDIENCE_USERS = 'COMBINATION_CUSTOM_AUDIENCE_USERS';
  const CONSTANT_CONTACTS_EMAIL_HASHES = 'CONSTANT_CONTACTS_EMAIL_HASHES';
  const CONTACT_IMPORTER = 'CONTACT_IMPORTER';
  const CONVERSION_PIXEL_HITS = 'CONVERSION_PIXEL_HITS';
  const COPY_PASTE_EMAIL_HASHES = 'COPY_PASTE_EMAIL_HASHES';
  const CUSTOM_AUDIENCE_USERS = 'CUSTOM_AUDIENCE_USERS';
  const CUSTOM_DATA_TARGETING = 'CUSTOM_DATA_TARGETING';
  const DATA_FILE = 'DATA_FILE';
  const DYNAMIC_RULE = 'DYNAMIC_RULE';
  const ENGAGEMENT_EVENT_USERS = 'ENGAGEMENT_EVENT_USERS';
  const EXPANDED_AUDIENCE = 'EXPANDED_AUDIENCE';
  const EXTERNAL_IDS = 'EXTERNAL_IDS';
  const EXTERNAL_IDS_MIX = 'EXTERNAL_IDS_MIX';
  const FACEBOOK_WIFI_EVENTS = 'FACEBOOK_WIFI_EVENTS';
  const FB_EVENT_SIGNALS = 'FB_EVENT_SIGNALS';
  const FB_PIXEL_HITS = 'FB_PIXEL_HITS';
  const GROUP_EVENTS = 'GROUP_EVENTS';
  const HASHES = 'HASHES';
  const HASHES_OR_USER_IDS = 'HASHES_OR_USER_IDS';
  const HOUSEHOLD_EXPANSION = 'HOUSEHOLD_EXPANSION';
  const IG_BUSINESS_EVENTS = 'IG_BUSINESS_EVENTS';
  const IG_PROMOTED_POST = 'IG_PROMOTED_POST';
  const INSTANT_ARTICLE_EVENTS = 'INSTANT_ARTICLE_EVENTS';
  const LOOKALIKE_PLATFORM = 'LOOKALIKE_PLATFORM';
  const MAIL_CHIMP_EMAIL_HASHES = 'MAIL_CHIMP_EMAIL_HASHES';
  const MARKETPLACE_LISTINGS = 'MARKETPLACE_LISTINGS';
  const MESSENGER_ONSITE_SUBSCRIPTION = 'MESSENGER_ONSITE_SUBSCRIPTION';
  const MOBILE_ADVERTISER_IDS = 'MOBILE_ADVERTISER_IDS';
  const MOBILE_APP_COMBINATION_EVENTS = 'MOBILE_APP_COMBINATION_EVENTS';
  const MOBILE_APP_CUSTOM_AUDIENCE_USERS = 'MOBILE_APP_CUSTOM_AUDIENCE_USERS';
  const MOBILE_APP_EVENTS = 'MOBILE_APP_EVENTS';
  const MULTICOUNTRY_COMBINATION = 'MULTICOUNTRY_COMBINATION';
  const MULTI_DATA_EVENTS = 'MULTI_DATA_EVENTS';
  const MULTI_EVENT_SOURCE = 'MULTI_EVENT_SOURCE';
  const MULTI_HASHES = 'MULTI_HASHES';
  const NOTHING = 'NOTHING';
  const OFFLINE_EVENT_USERS = 'OFFLINE_EVENT_USERS';
  const PAGE_FANS = 'PAGE_FANS';
  const PAGE_SMART_AUDIENCE = 'PAGE_SMART_AUDIENCE';
  const PARTNER_CATEGORY_USERS = 'PARTNER_CATEGORY_USERS';
  const PLACE_VISITS = 'PLACE_VISITS';
  const PLATFORM = 'PLATFORM';
  const PLATFORM_USERS = 'PLATFORM_USERS';
  const SEED_LIST = 'SEED_LIST';
  const SIGNAL_SOURCE = 'SIGNAL_SOURCE';
  const SMART_AUDIENCE = 'SMART_AUDIENCE';
  const STORE_VISIT_EVENTS = 'STORE_VISIT_EVENTS';
  const SUBSCRIBER_LIST = 'SUBSCRIBER_LIST';
  const S_EXPR = 'S_EXPR';
  const TOKENS = 'TOKENS';
  const USER_IDS = 'USER_IDS';
  const VIDEO_EVENTS = 'VIDEO_EVENTS';
  const VIDEO_EVENT_USERS = 'VIDEO_EVENT_USERS';
  const WEB_PIXEL_COMBINATION_EVENTS = 'WEB_PIXEL_COMBINATION_EVENTS';
  const WEB_PIXEL_HITS = 'WEB_PIXEL_HITS';
  const WEB_PIXEL_HITS_CUSTOM_AUDIENCE_USERS = 'WEB_PIXEL_HITS_CUSTOM_AUDIENCE_USERS';
  const WHATSAPP_SUBSCRIBER_POOL = 'WHATSAPP_SUBSCRIBER_POOL';
}
