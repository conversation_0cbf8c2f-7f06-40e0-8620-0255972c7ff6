<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static BusinessTimezoneIdValues getInstance()
 */
class BusinessTimezoneIdValues extends AbstractEnum {

  const VALUE_0 = '0';
  const VALUE_1 = '1';
  const VALUE_2 = '2';
  const VALUE_3 = '3';
  const VALUE_4 = '4';
  const VALUE_5 = '5';
  const VALUE_6 = '6';
  const VALUE_7 = '7';
  const VALUE_8 = '8';
  const VALUE_9 = '9';
  const VALUE_10 = '10';
  const VALUE_11 = '11';
  const VALUE_12 = '12';
  const VALUE_13 = '13';
  const VALUE_14 = '14';
  const VALUE_15 = '15';
  const VALUE_16 = '16';
  const VALUE_17 = '17';
  const VALUE_18 = '18';
  const VALUE_19 = '19';
  const VALUE_20 = '20';
  const VALUE_21 = '21';
  const VALUE_22 = '22';
  const VALUE_23 = '23';
  const VALUE_24 = '24';
  const VALUE_25 = '25';
  const VALUE_26 = '26';
  const VALUE_27 = '27';
  const VALUE_28 = '28';
  const VALUE_29 = '29';
  const VALUE_30 = '30';
  const VALUE_31 = '31';
  const VALUE_32 = '32';
  const VALUE_33 = '33';
  const VALUE_34 = '34';
  const VALUE_35 = '35';
  const VALUE_36 = '36';
  const VALUE_37 = '37';
  const VALUE_38 = '38';
  const VALUE_39 = '39';
  const VALUE_40 = '40';
  const VALUE_41 = '41';
  const VALUE_42 = '42';
  const VALUE_43 = '43';
  const VALUE_44 = '44';
  const VALUE_45 = '45';
  const VALUE_46 = '46';
  const VALUE_47 = '47';
  const VALUE_48 = '48';
  const VALUE_49 = '49';
  const VALUE_50 = '50';
  const VALUE_51 = '51';
  const VALUE_52 = '52';
  const VALUE_53 = '53';
  const VALUE_54 = '54';
  const VALUE_55 = '55';
  const VALUE_56 = '56';
  const VALUE_57 = '57';
  const VALUE_58 = '58';
  const VALUE_59 = '59';
  const VALUE_60 = '60';
  const VALUE_61 = '61';
  const VALUE_62 = '62';
  const VALUE_63 = '63';
  const VALUE_64 = '64';
  const VALUE_65 = '65';
  const VALUE_66 = '66';
  const VALUE_67 = '67';
  const VALUE_68 = '68';
  const VALUE_69 = '69';
  const VALUE_70 = '70';
  const VALUE_71 = '71';
  const VALUE_72 = '72';
  const VALUE_73 = '73';
  const VALUE_74 = '74';
  const VALUE_75 = '75';
  const VALUE_76 = '76';
  const VALUE_77 = '77';
  const VALUE_78 = '78';
  const VALUE_79 = '79';
  const VALUE_80 = '80';
  const VALUE_81 = '81';
  const VALUE_82 = '82';
  const VALUE_83 = '83';
  const VALUE_84 = '84';
  const VALUE_85 = '85';
  const VALUE_86 = '86';
  const VALUE_87 = '87';
  const VALUE_88 = '88';
  const VALUE_89 = '89';
  const VALUE_90 = '90';
  const VALUE_91 = '91';
  const VALUE_92 = '92';
  const VALUE_93 = '93';
  const VALUE_94 = '94';
  const VALUE_95 = '95';
  const VALUE_96 = '96';
  const VALUE_97 = '97';
  const VALUE_98 = '98';
  const VALUE_99 = '99';
  const VALUE_100 = '100';
  const VALUE_101 = '101';
  const VALUE_102 = '102';
  const VALUE_103 = '103';
  const VALUE_104 = '104';
  const VALUE_105 = '105';
  const VALUE_106 = '106';
  const VALUE_107 = '107';
  const VALUE_108 = '108';
  const VALUE_109 = '109';
  const VALUE_110 = '110';
  const VALUE_111 = '111';
  const VALUE_112 = '112';
  const VALUE_113 = '113';
  const VALUE_114 = '114';
  const VALUE_115 = '115';
  const VALUE_116 = '116';
  const VALUE_117 = '117';
  const VALUE_118 = '118';
  const VALUE_119 = '119';
  const VALUE_120 = '120';
  const VALUE_121 = '121';
  const VALUE_122 = '122';
  const VALUE_123 = '123';
  const VALUE_124 = '124';
  const VALUE_125 = '125';
  const VALUE_126 = '126';
  const VALUE_127 = '127';
  const VALUE_128 = '128';
  const VALUE_129 = '129';
  const VALUE_130 = '130';
  const VALUE_131 = '131';
  const VALUE_132 = '132';
  const VALUE_133 = '133';
  const VALUE_134 = '134';
  const VALUE_135 = '135';
  const VALUE_136 = '136';
  const VALUE_137 = '137';
  const VALUE_138 = '138';
  const VALUE_139 = '139';
  const VALUE_140 = '140';
  const VALUE_141 = '141';
  const VALUE_142 = '142';
  const VALUE_143 = '143';
  const VALUE_144 = '144';
  const VALUE_145 = '145';
  const VALUE_146 = '146';
  const VALUE_147 = '147';
  const VALUE_148 = '148';
  const VALUE_149 = '149';
  const VALUE_150 = '150';
  const VALUE_151 = '151';
  const VALUE_152 = '152';
  const VALUE_153 = '153';
  const VALUE_154 = '154';
  const VALUE_155 = '155';
  const VALUE_156 = '156';
  const VALUE_157 = '157';
  const VALUE_158 = '158';
  const VALUE_159 = '159';
  const VALUE_160 = '160';
  const VALUE_161 = '161';
  const VALUE_162 = '162';
  const VALUE_163 = '163';
  const VALUE_164 = '164';
  const VALUE_165 = '165';
  const VALUE_166 = '166';
  const VALUE_167 = '167';
  const VALUE_168 = '168';
  const VALUE_169 = '169';
  const VALUE_170 = '170';
  const VALUE_171 = '171';
  const VALUE_172 = '172';
  const VALUE_173 = '173';
  const VALUE_174 = '174';
  const VALUE_175 = '175';
  const VALUE_176 = '176';
  const VALUE_177 = '177';
  const VALUE_178 = '178';
  const VALUE_179 = '179';
  const VALUE_180 = '180';
  const VALUE_181 = '181';
  const VALUE_182 = '182';
  const VALUE_183 = '183';
  const VALUE_184 = '184';
  const VALUE_185 = '185';
  const VALUE_186 = '186';
  const VALUE_187 = '187';
  const VALUE_188 = '188';
  const VALUE_189 = '189';
  const VALUE_190 = '190';
  const VALUE_191 = '191';
  const VALUE_192 = '192';
  const VALUE_193 = '193';
  const VALUE_194 = '194';
  const VALUE_195 = '195';
  const VALUE_196 = '196';
  const VALUE_197 = '197';
  const VALUE_198 = '198';
  const VALUE_199 = '199';
  const VALUE_200 = '200';
  const VALUE_201 = '201';
  const VALUE_202 = '202';
  const VALUE_203 = '203';
  const VALUE_204 = '204';
  const VALUE_205 = '205';
  const VALUE_206 = '206';
  const VALUE_207 = '207';
  const VALUE_208 = '208';
  const VALUE_209 = '209';
  const VALUE_210 = '210';
  const VALUE_211 = '211';
  const VALUE_212 = '212';
  const VALUE_213 = '213';
  const VALUE_214 = '214';
  const VALUE_215 = '215';
  const VALUE_216 = '216';
  const VALUE_217 = '217';
  const VALUE_218 = '218';
  const VALUE_219 = '219';
  const VALUE_220 = '220';
  const VALUE_221 = '221';
  const VALUE_222 = '222';
  const VALUE_223 = '223';
  const VALUE_224 = '224';
  const VALUE_225 = '225';
  const VALUE_226 = '226';
  const VALUE_227 = '227';
  const VALUE_228 = '228';
  const VALUE_229 = '229';
  const VALUE_230 = '230';
  const VALUE_231 = '231';
  const VALUE_232 = '232';
  const VALUE_233 = '233';
  const VALUE_234 = '234';
  const VALUE_235 = '235';
  const VALUE_236 = '236';
  const VALUE_237 = '237';
  const VALUE_238 = '238';
  const VALUE_239 = '239';
  const VALUE_240 = '240';
  const VALUE_241 = '241';
  const VALUE_242 = '242';
  const VALUE_243 = '243';
  const VALUE_244 = '244';
  const VALUE_245 = '245';
  const VALUE_246 = '246';
  const VALUE_247 = '247';
  const VALUE_248 = '248';
  const VALUE_249 = '249';
  const VALUE_250 = '250';
  const VALUE_251 = '251';
  const VALUE_252 = '252';
  const VALUE_253 = '253';
  const VALUE_254 = '254';
  const VALUE_255 = '255';
  const VALUE_256 = '256';
  const VALUE_257 = '257';
  const VALUE_258 = '258';
  const VALUE_259 = '259';
  const VALUE_260 = '260';
  const VALUE_261 = '261';
  const VALUE_262 = '262';
  const VALUE_263 = '263';
  const VALUE_264 = '264';
  const VALUE_265 = '265';
  const VALUE_266 = '266';
  const VALUE_267 = '267';
  const VALUE_268 = '268';
  const VALUE_269 = '269';
  const VALUE_270 = '270';
  const VALUE_271 = '271';
  const VALUE_272 = '272';
  const VALUE_273 = '273';
  const VALUE_274 = '274';
  const VALUE_275 = '275';
  const VALUE_276 = '276';
  const VALUE_277 = '277';
  const VALUE_278 = '278';
  const VALUE_279 = '279';
  const VALUE_280 = '280';
  const VALUE_281 = '281';
  const VALUE_282 = '282';
  const VALUE_283 = '283';
  const VALUE_284 = '284';
  const VALUE_285 = '285';
  const VALUE_286 = '286';
  const VALUE_287 = '287';
  const VALUE_288 = '288';
  const VALUE_289 = '289';
  const VALUE_290 = '290';
  const VALUE_291 = '291';
  const VALUE_292 = '292';
  const VALUE_293 = '293';
  const VALUE_294 = '294';
  const VALUE_295 = '295';
  const VALUE_296 = '296';
  const VALUE_297 = '297';
  const VALUE_298 = '298';
  const VALUE_299 = '299';
  const VALUE_300 = '300';
  const VALUE_301 = '301';
  const VALUE_302 = '302';
  const VALUE_303 = '303';
  const VALUE_304 = '304';
  const VALUE_305 = '305';
  const VALUE_306 = '306';
  const VALUE_307 = '307';
  const VALUE_308 = '308';
  const VALUE_309 = '309';
  const VALUE_310 = '310';
  const VALUE_311 = '311';
  const VALUE_312 = '312';
  const VALUE_313 = '313';
  const VALUE_314 = '314';
  const VALUE_315 = '315';
  const VALUE_316 = '316';
  const VALUE_317 = '317';
  const VALUE_318 = '318';
  const VALUE_319 = '319';
  const VALUE_320 = '320';
  const VALUE_321 = '321';
  const VALUE_322 = '322';
  const VALUE_323 = '323';
  const VALUE_324 = '324';
  const VALUE_325 = '325';
  const VALUE_326 = '326';
  const VALUE_327 = '327';
  const VALUE_328 = '328';
  const VALUE_329 = '329';
  const VALUE_330 = '330';
  const VALUE_331 = '331';
  const VALUE_332 = '332';
  const VALUE_333 = '333';
  const VALUE_334 = '334';
  const VALUE_335 = '335';
  const VALUE_336 = '336';
  const VALUE_337 = '337';
  const VALUE_338 = '338';
  const VALUE_339 = '339';
  const VALUE_340 = '340';
  const VALUE_341 = '341';
  const VALUE_342 = '342';
  const VALUE_343 = '343';
  const VALUE_344 = '344';
  const VALUE_345 = '345';
  const VALUE_346 = '346';
  const VALUE_347 = '347';
  const VALUE_348 = '348';
  const VALUE_349 = '349';
  const VALUE_350 = '350';
  const VALUE_351 = '351';
  const VALUE_352 = '352';
  const VALUE_353 = '353';
  const VALUE_354 = '354';
  const VALUE_355 = '355';
  const VALUE_356 = '356';
  const VALUE_357 = '357';
  const VALUE_358 = '358';
  const VALUE_359 = '359';
  const VALUE_360 = '360';
  const VALUE_361 = '361';
  const VALUE_362 = '362';
  const VALUE_363 = '363';
  const VALUE_364 = '364';
  const VALUE_365 = '365';
  const VALUE_366 = '366';
  const VALUE_367 = '367';
  const VALUE_368 = '368';
  const VALUE_369 = '369';
  const VALUE_370 = '370';
  const VALUE_371 = '371';
  const VALUE_372 = '372';
  const VALUE_373 = '373';
  const VALUE_374 = '374';
  const VALUE_375 = '375';
  const VALUE_376 = '376';
  const VALUE_377 = '377';
  const VALUE_378 = '378';
  const VALUE_379 = '379';
  const VALUE_380 = '380';
  const VALUE_381 = '381';
  const VALUE_382 = '382';
  const VALUE_383 = '383';
  const VALUE_384 = '384';
  const VALUE_385 = '385';
  const VALUE_386 = '386';
  const VALUE_387 = '387';
  const VALUE_388 = '388';
  const VALUE_389 = '389';
  const VALUE_390 = '390';
  const VALUE_391 = '391';
  const VALUE_392 = '392';
  const VALUE_393 = '393';
  const VALUE_394 = '394';
  const VALUE_395 = '395';
  const VALUE_396 = '396';
  const VALUE_397 = '397';
  const VALUE_398 = '398';
  const VALUE_399 = '399';
  const VALUE_400 = '400';
  const VALUE_401 = '401';
  const VALUE_402 = '402';
  const VALUE_403 = '403';
  const VALUE_404 = '404';
  const VALUE_405 = '405';
  const VALUE_406 = '406';
  const VALUE_407 = '407';
  const VALUE_408 = '408';
  const VALUE_409 = '409';
  const VALUE_410 = '410';
  const VALUE_411 = '411';
  const VALUE_412 = '412';
  const VALUE_413 = '413';
  const VALUE_414 = '414';
  const VALUE_415 = '415';
  const VALUE_416 = '416';
  const VALUE_417 = '417';
  const VALUE_418 = '418';
  const VALUE_419 = '419';
  const VALUE_420 = '420';
  const VALUE_421 = '421';
  const VALUE_422 = '422';
  const VALUE_423 = '423';
  const VALUE_424 = '424';
  const VALUE_425 = '425';
  const VALUE_426 = '426';
  const VALUE_427 = '427';
  const VALUE_428 = '428';
  const VALUE_429 = '429';
  const VALUE_430 = '430';
  const VALUE_431 = '431';
  const VALUE_432 = '432';
  const VALUE_433 = '433';
  const VALUE_434 = '434';
  const VALUE_435 = '435';
  const VALUE_436 = '436';
  const VALUE_437 = '437';
  const VALUE_438 = '438';
  const VALUE_439 = '439';
  const VALUE_440 = '440';
  const VALUE_441 = '441';
  const VALUE_442 = '442';
  const VALUE_443 = '443';
  const VALUE_444 = '444';
  const VALUE_445 = '445';
  const VALUE_446 = '446';
  const VALUE_447 = '447';
  const VALUE_448 = '448';
  const VALUE_449 = '449';
  const VALUE_450 = '450';
  const VALUE_451 = '451';
  const VALUE_452 = '452';
  const VALUE_453 = '453';
  const VALUE_454 = '454';
  const VALUE_455 = '455';
  const VALUE_456 = '456';
  const VALUE_457 = '457';
  const VALUE_458 = '458';
  const VALUE_459 = '459';
  const VALUE_460 = '460';
  const VALUE_461 = '461';
  const VALUE_462 = '462';
  const VALUE_463 = '463';
  const VALUE_464 = '464';
  const VALUE_465 = '465';
  const VALUE_466 = '466';
  const VALUE_467 = '467';
  const VALUE_468 = '468';
  const VALUE_469 = '469';
  const VALUE_470 = '470';
  const VALUE_471 = '471';
  const VALUE_472 = '472';
  const VALUE_473 = '473';
  const VALUE_474 = '474';
  const VALUE_475 = '475';
  const VALUE_476 = '476';
  const VALUE_477 = '477';
  const VALUE_478 = '478';
  const VALUE_479 = '479';
  const VALUE_480 = '480';
}
