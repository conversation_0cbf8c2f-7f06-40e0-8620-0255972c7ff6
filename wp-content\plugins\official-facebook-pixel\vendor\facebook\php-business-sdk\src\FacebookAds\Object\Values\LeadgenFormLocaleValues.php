<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static LeadgenFormLocaleValues getInstance()
 */
class LeadgenFormLocaleValues extends AbstractEnum {

  const AR_AR = 'AR_AR';
  const CS_CZ = 'CS_CZ';
  const DA_DK = 'DA_DK';
  const DE_DE = 'DE_DE';
  const EL_GR = 'EL_GR';
  const EN_GB = 'EN_GB';
  const EN_US = 'EN_US';
  const ES_ES = 'ES_ES';
  const ES_LA = 'ES_LA';
  const FI_FI = 'FI_FI';
  const FR_FR = 'FR_FR';
  const HE_IL = 'HE_IL';
  const HI_IN = 'HI_IN';
  const HU_HU = 'HU_HU';
  const ID_ID = 'ID_ID';
  const IT_IT = 'IT_IT';
  const JA_JP = 'JA_JP';
  const KO_KR = 'KO_KR';
  const NB_NO = 'NB_NO';
  const NL_NL = 'NL_NL';
  const PL_PL = 'PL_PL';
  const PT_BR = 'PT_BR';
  const PT_PT = 'PT_PT';
  const RO_RO = 'RO_RO';
  const RU_RU = 'RU_RU';
  const SV_SE = 'SV_SE';
  const TH_TH = 'TH_TH';
  const TR_TR = 'TR_TR';
  const VI_VN = 'VI_VN';
  const ZH_CN = 'ZH_CN';
  const ZH_HK = 'ZH_HK';
  const ZH_TW = 'ZH_TW';
}
