<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

use FacebookAds\Enum\AbstractEnum;

/**
 * This class is auto-generated.
 *
 * For any issues or feature requests related to this class, please let us know
 * on github and we'll fix in our codegen framework. We'll not be able to accept
 * pull request for this class.
 *
 * @method static CustomAudienceDataSourceTypeValues getInstance()
 */
class CustomAudienceDataSourceTypeValues extends AbstractEnum {

  const CONTACT_IMPORTER = 'CONTACT_IMPORTER';
  const COPY_PASTE = 'COPY_PASTE';
  const EVENT_BASED = 'EVENT_BASED';
  const FILE_IMPORTED = 'FILE_IMPORTED';
  const HOUSEHOLD_AUDIENCE = 'HOUSEHOLD_AUDIENCE';
  const SEED_BASED = 'SEED_BASED';
  const THIRD_PARTY_IMPORTED = 'THIRD_PARTY_IMPORTED';
  const UNKNOWN = 'UNKNOWN';
}
