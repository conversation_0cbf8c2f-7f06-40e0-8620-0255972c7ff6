<?php
 /*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the license found in the
 * LICENSE file in the root directory of this source tree.
 */

namespace FacebookAds\Object\Values;

/**
 * @deprecated use AdSetBillingEventValues instead
 *
 * @method static BillingEvents getInstance()
 */
class BillingEvents extends AdSetBillingEventValues {}
